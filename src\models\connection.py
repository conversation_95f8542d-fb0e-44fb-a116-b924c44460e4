from dataclasses import dataclass

from marshmallow import fields
from nolicore.adapters.db.model import AttributesSchema
from onexapis.models.connection import ConnectionModel, ConnectionSchema, ConnectionAttributes


@dataclass
class BambufitSettings:
    domain: str
    username: str
    password: str


class BambufitSettingsSchema(AttributesSchema):
    domain = fields.Str(required=True)
    username = fields.Str(required=True)
    password = fields.Str(required=True)

    def sample(self):
        return {
            "domain": "domain",
            "username": "username",
            "password": "password",
        }


@dataclass
class BambufitConnectionAttributes(ConnectionAttributes):
    settings: BambufitSettings = None


class BambufitConnectionSchema(ConnectionSchema):
    settings = fields.Nested(BambufitSettingsSchema(BambufitSettings), required=True)

    def sample(self):
        return {
            **super(BambufitConnectionSchema, self).sample(),
            'settings': BambufitSettingsSchema(BambufitSettings).sample()
        }


class BambufitConnection(ConnectionModel):
    attributes: BambufitConnectionAttributes = None
    attributes_schema = BambufitConnectionSchema
    attributes_class = BambufitConnectionAttributes
