import os

from src.actions.customer.sync_customer import run
from src.apis.booking import _list
from src.apis.customer import get_customer_by_code, get_customer_by_id, payments


def handler(event, context):
    function_name = os.environ.get('FUNCTION_HANDLER', 'handler1')
    if function_name == 'BookingsApi':
        return _list(event, context)
    elif function_name == 'SyncCustomer':
        return run(event, context)
    elif function_name == 'CustomersApiByCode':
        return get_customer_by_code(event, context)
    elif function_name == 'CustomersApiById':
        return get_customer_by_id(event, context)
    elif function_name == 'PaymentsApi':
        return payments(event, context)
    else:
        return {
            'statusCode': 400,
            'body': 'Invalid handler'
        }
