import os

from src.actions.customer.sync_customer import run
from src.apis.booking import _list
from src.apis.customer import get_customer_by_code, get_customer_by_id, payments
from src.apis.auth import schema, auth_link, test_connection, callback
from src.apis.actions import webhook, list_actions, init_actions


def handler(event, context):
    function_name = os.environ.get('FUNCTION_HANDLER', 'handler1')

    # Booking functions
    if function_name == 'BookingsApi':
        return _list(event, context)

    # Customer functions
    elif function_name == 'SyncCustomer':
        return run(event, context)
    elif function_name == 'CustomersApiByCode':
        return get_customer_by_code(event, context)
    elif function_name == 'CustomersApiById':
        return get_customer_by_id(event, context)
    elif function_name == 'PaymentsApi':
        return payments(event, context)

    # Auth functions
    elif function_name == 'schema':
        return schema(event, context)
    elif function_name == 'authLink':
        return auth_link(event, context)
    elif function_name == 'testConnection':
        return test_connection(event, context)
    elif function_name == 'callback':
        return callback(event, context)

    # Action functions
    elif function_name == 'sampleWebhook':
        return webhook(event, context)
    elif function_name == 'actions':
        return list_actions(event, context)
    elif function_name == 'initActions':
        return init_actions(event, context)

    else:
        return {
            'statusCode': 400,
            'body': f'Invalid handler: {function_name}'
        }
