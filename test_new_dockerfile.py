#!/usr/bin/env python3
"""
Test script to verify the new Dockerfile fixes Chrome compatibility issues
"""
import os
import sys

# Set up environment variables like AWS Lambda
os.environ['ENV'] = 'dev'
os.environ['USER_POOL_ID'] = 'ap-southeast-1_Tmn6Tbm0H'
os.environ['APP_CLIENT_ID'] = '3fh2s28e3g8l23068d6j573l9a'
os.environ['LOG_LEVEL'] = '40'
os.environ['AWS_LAMBDA_FUNCTION_NAME'] = 'test-lambda'  # Simulate Lambda environment

# Set Python path
sys.path.insert(0, '.')

def test_dockerfile_changes():
    """Test that the Dockerfile changes are correct"""
    print("🔍 Testing Dockerfile changes...")
    
    # Read the Dockerfile
    try:
        with open('Dockerfile', 'r') as f:
            dockerfile_content = f.read()
        
        # Check for AWS Lambda base image
        if 'FROM public.ecr.aws/lambda/python:3.9' in dockerfile_content:
            print("  ✅ Using AWS Lambda Python base image")
        else:
            print("  ❌ Not using AWS Lambda base image")
            return False
        
        # Check for Chrome installation
        if 'google-chrome-stable-current_x86_64.rpm' in dockerfile_content:
            print("  ✅ Installing latest Chrome from Google")
        else:
            print("  ❌ Not installing Chrome from Google")
            return False
        
        # Check for dynamic ChromeDriver version detection
        if 'CHROMEDRIVER_VERSION=' in dockerfile_content and 'LATEST_RELEASE_' in dockerfile_content:
            print("  ✅ Using dynamic ChromeDriver version detection")
        else:
            print("  ❌ Not using dynamic ChromeDriver version detection")
            return False
        
        # Check that old ChromeDriver files are not being copied
        if 'src/libs/otp/chromedriver' not in dockerfile_content:
            print("  ✅ Not using old ChromeDriver files from otp folder")
        else:
            print("  ❌ Still using old ChromeDriver files from otp folder")
            return False
        
        # Check for Lambda handler
        if 'CMD ["src.main.handler"]' in dockerfile_content:
            print("  ✅ Using correct Lambda handler format")
        else:
            print("  ❌ Not using correct Lambda handler format")
            return False
        
        print("  ✅ All Dockerfile changes look correct")
        return True
        
    except FileNotFoundError:
        print("  ❌ Dockerfile not found")
        return False
    except Exception as e:
        print(f"  ❌ Error reading Dockerfile: {e}")
        return False

def test_chrome_logic_compatibility():
    """Test that the Chrome logic in bambu.py is compatible"""
    print("🔍 Testing Chrome logic compatibility...")
    
    try:
        from src.libs.bambu import Bambufit
        
        # Create instance
        bambu = Bambufit('test_domain', 'test_user', 'test_pass')
        print("  ✅ Bambufit instance created successfully")
        
        # Test environment detection logic
        env = os.environ.get('ENV')
        is_lambda = os.environ.get('AWS_LAMBDA_FUNCTION_NAME') is not None
        is_container = os.path.exists('/opt/chrome/chrome')
        
        should_use_container_chrome = is_lambda or is_container or (env and env in ['dev', 'prod'])
        
        print(f"  ENV: {env}")
        print(f"  AWS_LAMBDA_FUNCTION_NAME: {is_lambda}")
        print(f"  /opt/chrome/chrome exists: {is_container}")
        print(f"  Should use container Chrome: {should_use_container_chrome}")
        
        if should_use_container_chrome:
            print("  ✅ Chrome logic will use container paths")
        else:
            print("  ⚠️  Chrome logic will not use container paths (expected for local testing)")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error testing Chrome logic: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_serverless_config():
    """Test that serverless.yml includes Chrome driver files"""
    print("🔍 Testing serverless.yml configuration...")
    
    try:
        with open('serverless.yml', 'r') as f:
            serverless_content = f.read()
        
        # Check that Chrome driver files are not excluded
        if '"!src/libs/otp"' not in serverless_content:
            print("  ✅ Chrome driver files are not excluded from deployment")
        else:
            print("  ❌ Chrome driver files are still excluded from deployment")
            return False
        
        # Check for ECR image configuration
        if 'ecr:' in serverless_content and 'images:' in serverless_content:
            print("  ✅ ECR image configuration found")
        else:
            print("  ❌ ECR image configuration not found")
            return False
        
        return True
        
    except FileNotFoundError:
        print("  ❌ serverless.yml not found")
        return False
    except Exception as e:
        print(f"  ❌ Error reading serverless.yml: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Testing new Dockerfile and Chrome compatibility fixes\n")
    
    tests = [
        ("Dockerfile Changes", test_dockerfile_changes),
        ("Chrome Logic Compatibility", test_chrome_logic_compatibility),
        ("Serverless Configuration", test_serverless_config),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"Running: {test_name}")
        print('='*50)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    print(f"\n{'='*50}")
    print("SUMMARY")
    print('='*50)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status}: {test_name}")
        if not result:
            all_passed = False
    
    print(f"\n{'='*50}")
    print("EXPECTED IMPROVEMENTS")
    print('='*50)
    print("✅ Chrome and ChromeDriver versions will be automatically matched")
    print("✅ Latest stable Chrome will be installed from Google")
    print("✅ ChromeDriver will be downloaded to match Chrome version")
    print("✅ No more 'Missing or invalid capabilities' error")
    print("✅ Compatible with AWS Lambda environment")
    
    if all_passed:
        print("\n🎉 All tests passed! The new Dockerfile should resolve Chrome compatibility issues.")
        print("\n📋 Next steps:")
        print("1. Build the Docker image: docker build -t bambufit:latest .")
        print("2. Deploy to AWS Lambda using serverless")
        print("3. Test the deployed function")
    else:
        print("\n⚠️  Some tests failed. Please review the issues above.")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
