import unittest

from tests.base import BaseTestCase
from src.apis.auth import schema, test_connection
from tests.apis.auth.data_auth import auth_event, connection_event


class AuthTestCase(BaseTestCase):
    def test_schema(self):
        schema(auth_event, {})
        schema(auth_event, {})
        self.assertTrue(True)

    def test_connection(self):
        test_connection(connection_event, {})
        self.assertTrue(True)


if __name__ == '__main__':
    # begin the unittest.main()
    unittest.main()
