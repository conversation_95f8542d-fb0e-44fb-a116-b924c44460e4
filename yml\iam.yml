role:
  statements:
    - Effect: Allow
      Action:
        - dynamodb:Get*
        - dynamodb:PutItem
        - dynamodb:UpdateItem
        - dynamodb:DeleteItem
        - dynamodb:Scan
        - dynamodb:BatchWriteItem
      Resource:
        - "arn:aws:dynamodb:${opt:region, self:provider.region}:*:table/execution"
        - "arn:aws:dynamodb:${opt:region, self:provider.region}:*:table/connection"
        - "arn:aws:dynamodb:${opt:region, self:provider.region}:*:table/action"
        - "arn:aws:dynamodb:${opt:region, self:provider.region}:*:table/flow"
        - "arn:aws:dynamodb:${opt:region, self:provider.region}:*:table/actionRecord"
        - "arn:aws:dynamodb:${opt:region, self:provider.region}:*:table/webhook"
