import unittest
from tests.base import BaseTestCase
from src.apis.customer import get_customer_by_code, get_customer_by_id, payments
from tests.apis.customer.data_customer import customer_event


class CustomerApiTestCase(BaseTestCase):
    def test_customer_api(self):
        get_customer_by_code(customer_event, {})
        get_customer_by_id(customer_event, {})
        self.assertTrue(True)
        
    def test_payments_api(self):
        payments(customer_event, {})


if __name__ == '__main__':
    # begin the unittest.main()
    unittest.main()
