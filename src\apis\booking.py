from nolicore.utils.aws.decorator import as_api
from nolicore.utils.aws.request import ApiGatewayRequest

from src.libs.bambu import Bambufit
from src.libs.bookie import bambu_to_bookie


@as_api()
def _list(api_request: ApiGatewayRequest):
    connection_id = api_request.path_parameters['connection_id']
    bambufit = Bambufit.from_connection(connection_id, api_request.company_id)
    schedule_date = api_request.query_string_parameters['schedule_date']
    bookings = bambufit.get_booking(_from=schedule_date, _to=schedule_date)
    return [bambu_to_bookie(b) for b in bookings]
