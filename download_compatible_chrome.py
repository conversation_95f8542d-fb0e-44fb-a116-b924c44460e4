#!/usr/bin/env python3
"""
Download Chrome version compatible with ChromeDriver 2.43.600233
ChromeDriver 2.43 supports Chrome versions 69-71
"""
import os
import requests
import zipfile
import shutil
from pathlib import Path

def download_compatible_chrome():
    """Download Chrome version 70 (compatible with ChromeDriver 2.43)"""
    print("🔍 Downloading Chrome version 70 (compatible with ChromeDriver 2.43.600233)")
    
    # Chrome 70 build that's compatible with ChromeDriver 2.43
    # Using Chromium builds from Google's archive
    chrome_build = "591479"  # Chrome 70.0.3538.77
    url = f"https://storage.googleapis.com/chromium-browser-snapshots/Linux_x64/{chrome_build}/chrome-linux.zip"
    
    otp_dir = Path("src/libs/otp")
    temp_dir = Path("temp_chrome_download")
    
    if not otp_dir.exists():
        print(f"❌ OTP directory not found: {otp_dir}")
        return False
    
    temp_dir.mkdir(exist_ok=True)
    
    try:
        print(f"📥 Downloading Chrome 70 from build {chrome_build}...")
        chrome_zip = temp_dir / "chrome-linux.zip"
        
        response = requests.get(url, stream=True, timeout=60)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        downloaded = 0
        
        with open(chrome_zip, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)
                    if total_size > 0:
                        percent = (downloaded / total_size) * 100
                        print(f"\r   Progress: {percent:.1f}%", end='', flush=True)
        
        print(f"\n✅ Downloaded Chrome 70 successfully")
        
        # Extract Chrome
        print("📦 Extracting Chrome...")
        with zipfile.ZipFile(chrome_zip, 'r') as zip_ref:
            zip_ref.extractall(temp_dir)
        
        # Find the chrome binary
        chrome_binary = temp_dir / "chrome-linux" / "chrome"
        if chrome_binary.exists():
            # Backup old file
            old_chrome = otp_dir / "headless-chromium"
            if old_chrome.exists():
                backup_chrome = otp_dir / "headless-chromium.backup.new"
                shutil.copy2(old_chrome, backup_chrome)
                print(f"📋 Backed up current Chrome to {backup_chrome}")
            
            # Install Chrome 70
            shutil.copy2(chrome_binary, old_chrome)
            os.chmod(old_chrome, 0o755)
            print("✅ Chrome 70 installed successfully")
            
            # Verify
            print("🔍 Verifying installation...")
            print(f"   Chrome binary size: {old_chrome.stat().st_size / 1024 / 1024:.1f} MB")
            print(f"   Chrome binary permissions: {oct(old_chrome.stat().st_mode)}")
            
            return True
        else:
            print("❌ Chrome binary not found in extracted files")
            return False
            
    except Exception as e:
        print(f"❌ Error downloading Chrome: {e}")
        return False
    
    finally:
        # Cleanup
        if temp_dir.exists():
            shutil.rmtree(temp_dir)

def verify_compatibility():
    """Verify ChromeDriver and Chrome compatibility"""
    print("\n🔍 Verifying ChromeDriver and Chrome compatibility...")
    
    otp_dir = Path("src/libs/otp")
    chrome_path = otp_dir / "headless-chromium"
    chromedriver_path = otp_dir / "chromedriver"
    
    if not chrome_path.exists():
        print("❌ Chrome binary not found")
        return False
    
    if not chromedriver_path.exists():
        print("❌ ChromeDriver binary not found")
        return False
    
    print("✅ Both Chrome and ChromeDriver binaries exist")
    print(f"   Chrome size: {chrome_path.stat().st_size / 1024 / 1024:.1f} MB")
    print(f"   ChromeDriver size: {chromedriver_path.stat().st_size / 1024 / 1024:.1f} MB")
    
    # Note: We can't test version compatibility on Windows, but we know:
    # ChromeDriver 2.43.600233 supports Chrome 69-71
    # We downloaded Chrome 70, so they should be compatible
    
    print("📋 Compatibility info:")
    print("   ChromeDriver: 2.43.600233 (supports Chrome 69-71)")
    print("   Chrome: 70.0.3538.77 (compatible)")
    print("   ✅ Should be compatible!")
    
    return True

def main():
    print("🚀 Chrome Compatibility Fixer")
    print("=" * 50)
    print("This will download Chrome 70 to work with your existing ChromeDriver 2.43")
    
    response = input("\n❓ Continue with download? (y/N): ").strip().lower()
    if response not in ['y', 'yes']:
        print("❌ Download cancelled by user")
        return False
    
    if download_compatible_chrome():
        if verify_compatibility():
            print("\n🎉 Chrome compatibility fix completed!")
            print("\n📋 Next steps:")
            print("1. Build Docker image: docker build -t bambufit:compatible .")
            print("2. Deploy: serverless deploy --stage prod")
            print("3. Test the application")
            return True
    
    print("\n❌ Chrome compatibility fix failed!")
    return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
