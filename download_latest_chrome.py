#!/usr/bin/env python3
"""
Download latest compatible Chrome and ChromeDriver to src/libs/otp folder
"""
import os
import requests
import zipfile
import shutil
import json
from pathlib import Path

def get_latest_chrome_for_testing():
    """Get latest Chrome for Testing version"""
    try:
        url = "https://googlechromelabs.github.io/chrome-for-testing/last-known-good-versions.json"
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        
        data = response.json()
        stable_version = data['channels']['Stable']['version']
        print(f"📋 Latest Chrome for Testing version: {stable_version}")
        return stable_version
        
    except Exception as e:
        print(f"❌ Error getting Chrome version: {e}")
        # Fallback to known stable version
        fallback_version = "119.0.6045.105"
        print(f"🔄 Using fallback version: {fallback_version}")
        return fallback_version

def download_chrome(version, temp_dir):
    """Download Chrome for Testing"""
    print(f"📥 Downloading Chrome {version}...")
    
    # Chrome for Testing download URL
    url = f"https://storage.googleapis.com/chrome-for-testing-public/{version}/linux64/chrome-linux64.zip"
    chrome_zip = temp_dir / "chrome-linux64.zip"
    
    try:
        response = requests.get(url, stream=True, timeout=60)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        downloaded = 0
        
        with open(chrome_zip, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)
                    if total_size > 0:
                        percent = (downloaded / total_size) * 100
                        print(f"\r   Progress: {percent:.1f}%", end='', flush=True)
        
        print(f"\n✅ Downloaded Chrome {version} successfully")
        return chrome_zip
        
    except Exception as e:
        print(f"\n❌ Error downloading Chrome: {e}")
        return None

def download_chromedriver(version, temp_dir):
    """Download ChromeDriver for Testing"""
    print(f"📥 Downloading ChromeDriver {version}...")
    
    # ChromeDriver for Testing download URL
    url = f"https://storage.googleapis.com/chrome-for-testing-public/{version}/linux64/chromedriver-linux64.zip"
    chromedriver_zip = temp_dir / "chromedriver-linux64.zip"
    
    try:
        response = requests.get(url, stream=True, timeout=60)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        downloaded = 0
        
        with open(chromedriver_zip, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)
                    if total_size > 0:
                        percent = (downloaded / total_size) * 100
                        print(f"\r   Progress: {percent:.1f}%", end='', flush=True)
        
        print(f"\n✅ Downloaded ChromeDriver {version} successfully")
        return chromedriver_zip
        
    except Exception as e:
        print(f"\n❌ Error downloading ChromeDriver: {e}")
        return None

def extract_and_install(chrome_zip, chromedriver_zip, otp_dir):
    """Extract and install Chrome and ChromeDriver to otp directory"""
    print("📦 Extracting and installing files...")
    
    temp_extract = Path("temp_extract_latest")
    temp_extract.mkdir(exist_ok=True)
    
    try:
        # Extract Chrome
        if chrome_zip and chrome_zip.exists():
            print("   Extracting Chrome...")
            with zipfile.ZipFile(chrome_zip, 'r') as zip_ref:
                zip_ref.extractall(temp_extract)
            
            # Find Chrome binary
            chrome_binary = temp_extract / "chrome-linux64" / "chrome"
            if chrome_binary.exists():
                # Backup old file
                old_chrome = otp_dir / "headless-chromium"
                if old_chrome.exists():
                    backup_chrome = otp_dir / "headless-chromium.backup.latest"
                    shutil.copy2(old_chrome, backup_chrome)
                    print(f"   📋 Backed up old Chrome to {backup_chrome}")
                
                # Install new Chrome
                shutil.copy2(chrome_binary, old_chrome)
                os.chmod(old_chrome, 0o755)
                print("   ✅ Chrome installed successfully")
            else:
                print("   ❌ Chrome binary not found in extracted files")
                return False
        
        # Extract ChromeDriver
        if chromedriver_zip and chromedriver_zip.exists():
            print("   Extracting ChromeDriver...")
            with zipfile.ZipFile(chromedriver_zip, 'r') as zip_ref:
                zip_ref.extractall(temp_extract)
            
            # Find ChromeDriver binary
            chromedriver_binary = temp_extract / "chromedriver-linux64" / "chromedriver"
            if chromedriver_binary.exists():
                # Backup old file
                old_chromedriver = otp_dir / "chromedriver"
                if old_chromedriver.exists():
                    backup_chromedriver = otp_dir / "chromedriver.backup.latest"
                    shutil.copy2(old_chromedriver, backup_chromedriver)
                    print(f"   📋 Backed up old ChromeDriver to {backup_chromedriver}")
                
                # Install new ChromeDriver
                shutil.copy2(chromedriver_binary, old_chromedriver)
                os.chmod(old_chromedriver, 0o755)
                print("   ✅ ChromeDriver installed successfully")
            else:
                print("   ❌ ChromeDriver binary not found in extracted files")
                return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error during extraction: {e}")
        return False
    
    finally:
        # Cleanup temp directory
        if temp_extract.exists():
            shutil.rmtree(temp_extract)

def verify_installation(otp_dir, version):
    """Verify installation"""
    print("🔍 Verifying installation...")
    
    chrome_path = otp_dir / "headless-chromium"
    chromedriver_path = otp_dir / "chromedriver"
    
    if not chrome_path.exists():
        print("   ❌ Chrome binary not found")
        return False
    
    if not chromedriver_path.exists():
        print("   ❌ ChromeDriver binary not found")
        return False
    
    print(f"   ✅ Chrome size: {chrome_path.stat().st_size / 1024 / 1024:.1f} MB")
    print(f"   ✅ ChromeDriver size: {chromedriver_path.stat().st_size / 1024 / 1024:.1f} MB")
    print(f"   ✅ Both files have executable permissions")
    
    # Save version info
    version_file = otp_dir / "version_info.txt"
    with open(version_file, 'w') as f:
        f.write(f"Chrome for Testing version: {version}\n")
        f.write(f"ChromeDriver version: {version}\n")
        f.write(f"Downloaded on: {__import__('datetime').datetime.now().isoformat()}\n")
    
    print(f"   📋 Version info saved to {version_file}")
    return True

def main():
    """Main function"""
    print("🚀 Chrome for Testing Downloader")
    print("=" * 50)
    print("This will download the latest Chrome for Testing and ChromeDriver")
    
    # Setup directories
    otp_dir = Path("src/libs/otp")
    temp_dir = Path("temp_downloads_latest")
    
    if not otp_dir.exists():
        print(f"❌ OTP directory not found: {otp_dir}")
        print("   Please run this script from the project root directory")
        return False
    
    temp_dir.mkdir(exist_ok=True)
    
    try:
        # Get latest version
        version = get_latest_chrome_for_testing()
        
        print(f"\n📋 Will download Chrome for Testing version: {version}")
        print("   This includes both Chrome and ChromeDriver with guaranteed compatibility")
        
        # Confirm with user
        response = input("\n❓ Continue with download? (y/N): ").strip().lower()
        if response not in ['y', 'yes']:
            print("❌ Download cancelled by user")
            return False
        
        # Download files
        print("\n📥 Starting downloads...")
        chrome_zip = download_chrome(version, temp_dir)
        chromedriver_zip = download_chromedriver(version, temp_dir)
        
        if not chrome_zip or not chromedriver_zip:
            print("❌ Download failed")
            return False
        
        # Install files
        print("\n📦 Installing files...")
        if not extract_and_install(chrome_zip, chromedriver_zip, otp_dir):
            print("❌ Installation failed")
            return False
        
        # Verify installation
        if not verify_installation(otp_dir, version):
            print("❌ Verification failed")
            return False
        
        print("\n🎉 Chrome for Testing updated successfully!")
        print(f"   Chrome version: {version}")
        print(f"   ChromeDriver version: {version}")
        print("\n📋 Next steps:")
        print("1. Build Docker image: docker build -t bambufit:latest .")
        print("2. Deploy: serverless deploy --stage prod")
        print("3. Test the application")
        
        return True
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False
    
    finally:
        # Cleanup temp directory
        if temp_dir.exists():
            shutil.rmtree(temp_dir)
            print(f"🧹 Cleaned up temporary files")

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
