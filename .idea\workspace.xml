<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="0a896658-49fa-4e19-8729-faddaec08335" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/.env" beforeDir="false" afterPath="$PROJECT_DIR$/.env" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.gitignore" beforeDir="false" afterPath="$PROJECT_DIR$/.gitignore" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Dockerfile" beforeDir="false" afterPath="$PROJECT_DIR$/Dockerfile" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/requirements.txt" beforeDir="false" afterPath="$PROJECT_DIR$/requirements.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/serverless.yml" beforeDir="false" afterPath="$PROJECT_DIR$/serverless.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/apis/customer.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/apis/customer.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/libs/bambu.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/libs/bambu.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/libs/bookie.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/libs/bookie.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/libs/otp/chromedriver" beforeDir="false" afterPath="$PROJECT_DIR$/src/libs/otp/chromedriver" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/libs/otp/headless-chromium" beforeDir="false" afterPath="$PROJECT_DIR$/src/libs/otp/headless-chromium" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/main.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yml/custom.yml" beforeDir="false" afterPath="$PROJECT_DIR$/yml/custom.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yml/functions/common/action.yml" beforeDir="false" afterPath="$PROJECT_DIR$/yml/functions/common/action.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yml/functions/common/auth.yml" beforeDir="false" afterPath="$PROJECT_DIR$/yml/functions/common/auth.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yml/functions/customer.yml" beforeDir="false" afterPath="$PROJECT_DIR$/yml/functions/customer.yml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2yOABqp0B74EDvm4w26qSEuLchp" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Python.bambu.executor&quot;: &quot;Debug&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;dev&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;com.jetbrains.python.configuration.PyActiveSdkModuleConfigurable&quot;
  }
}</component>
  <component name="RunManager">
    <configuration name="bambu" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="bambufit" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/src/libs" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/src/libs/bambu.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Python.bambu" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-python-sdk-dfff61a61320-9cdd278e9d02-com.jetbrains.pycharm.community.sharedIndexes.bundled-PC-251.25410.159" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="0a896658-49fa-4e19-8729-faddaec08335" name="Changes" comment="" />
      <created>1749695113375</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1749695113375</updated>
    </task>
    <servers />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/src/libs/bambu.py</url>
          <line>543</line>
          <option name="timeStamp" value="25" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/src/libs/bambu.py</url>
          <line>541</line>
          <option name="timeStamp" value="27" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/src/libs/bambu.py</url>
          <line>540</line>
          <option name="timeStamp" value="28" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/src/libs/bambu.py</url>
          <line>509</line>
          <option name="timeStamp" value="34" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/src/libs/bambu.py</url>
          <line>538</line>
          <option name="timeStamp" value="35" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/src/libs/bambu.py</url>
          <line>93</line>
          <option name="timeStamp" value="36" />
        </line-breakpoint>
      </breakpoints>
      <default-breakpoints>
        <breakpoint type="python-exception">
          <properties notifyOnTerminate="true" exception="BaseException">
            <option name="notifyOnTerminate" value="true" />
          </properties>
        </breakpoint>
      </default-breakpoints>
    </breakpoint-manager>
  </component>
</project>