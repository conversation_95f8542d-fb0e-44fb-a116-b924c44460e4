SyncCustomer:
  image:
    name: bambufit
  timeout: 900
  memorySize: 1024
  environment:
    FUNCTION_HANDLER: SyncCustomer

CustomersApiByCode:
  image:
    name: bambufit
  environment:
    FUNCTION_HANDLER: CustomersApiByCode
  events:
    - httpApi:
        path: /{connection_id}/customers/{customer_id}
        method: get
        authorizer:
          name: onexapisAuthorizer

CustomersApiById:
  image:
    name: bambufit
  environment:
    FUNCTION_HANDLER: CustomersApiById
  events:
    - httpApi:
        path: /{connection_id}/customers/by_id/{customer_id}
        method: get
        authorizer:
          name: onexapisAuthorizer

PaymentsApi:
  image:
    name: bambufit
  environment:
    FUNCTION_HANDLER: PaymentsApi
  events:
    - httpApi:
        path: /{connection_id}/payments
        method: get
        authorizer:
          name: onexapisAuthorizer