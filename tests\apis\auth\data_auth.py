import os

os.environ['USER_POOL_ID'] = 'ap-southeast-1_BCrxqt1qo'
os.environ['APP_CLIENT_ID'] = '3p9avbd007gpqt6dlr8js28pup'
os.environ['ENV'] = 'dev'
os.environ['LOG_LEVEL'] = '20'

auth_event = {'resource': '/auth/{connectionId}/auth_link', 'path': '/auth/42053fff-2db2-44fb-bacb-4a63845ac693/auth_link', 'httpMethod': 'GET', 'headers': {'Accept': '*/*', 'Accept-Encoding': 'gzip, deflate, br', 'Authorization': 'Bearer eyJraWQiOiJabUpwWksyd0tqWlVzak9SOGpjaXZxWVNKZVhPbFV3YzRKSjlLSHozeEFJPSIsImFsZyI6IlJTMjU2In0.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.UsFbQrwTHWnbxslApx05Y4Tx6hkcTg8CBX_RdVrOjsUDgfcPBMk1Elbna4JzGNx0LkH___goT21ZXJciZ3gVIsJQKVD9QsXF7uYQ-pnyxB1HoabkgdA8cmR_n2EfUW49QPW68aJakOVmQA6Xv4QW7Hi0aXR5guK1wCNQn2bl4xxErET170GN9Or-XvvJUib-9cOXBpCtYEDkva_CyqHH00Ek9GdFCFeRBFlnPxapEeTXh2G9ClzMNzbLWLSRmVYu_Vju6_Irjx4AltSHcAxMDgK5cMqr5DahoKGr9sQD_NIztpmRgpzb0uJMx815X1UICWoEVRR5NcE6jSg5wWLWxw', 'CloudFront-Forwarded-Proto': 'https', 'CloudFront-Is-Desktop-Viewer': 'true', 'CloudFront-Is-Mobile-Viewer': 'false', 'CloudFront-Is-SmartTV-Viewer': 'false', 'CloudFront-Is-Tablet-Viewer': 'false', 'CloudFront-Viewer-ASN': '18403', 'CloudFront-Viewer-Country': 'VN', 'Host': 'jz6rptxoo9.execute-api.ap-southeast-1.amazonaws.com', 'Postman-Token': '5455a33c-eb9a-40ca-a00a-f13b39428486', 'User-Agent': 'PostmanRuntime/7.32.2', 'Via': '1.1 e0c6c6bdfde70ef727e4bf48a0402bf6.cloudfront.net (CloudFront)', 'X-Amz-Cf-Id': 'q9RFTe9PHyDzF5AzOlml91uBLSNiDmJXio3ndagGNIiSXYx1Q4kU9Q==', 'X-Amzn-Trace-Id': 'Root=1-644e7373-2874307261cd8ec765af4719', 'X-Forwarded-For': '*************, ************', 'X-Forwarded-Port': '443', 'X-Forwarded-Proto': 'https'}, 'multiValueHeaders': {'Accept': ['*/*'], 'Accept-Encoding': ['gzip, deflate, br'], 'Authorization': ['Bearer eyJraWQiOiJabUpwWksyd0tqWlVzak9SOGpjaXZxWVNKZVhPbFV3YzRKSjlLSHozeEFJPSIsImFsZyI6IlJTMjU2In0.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.UsFbQrwTHWnbxslApx05Y4Tx6hkcTg8CBX_RdVrOjsUDgfcPBMk1Elbna4JzGNx0LkH___goT21ZXJciZ3gVIsJQKVD9QsXF7uYQ-pnyxB1HoabkgdA8cmR_n2EfUW49QPW68aJakOVmQA6Xv4QW7Hi0aXR5guK1wCNQn2bl4xxErET170GN9Or-XvvJUib-9cOXBpCtYEDkva_CyqHH00Ek9GdFCFeRBFlnPxapEeTXh2G9ClzMNzbLWLSRmVYu_Vju6_Irjx4AltSHcAxMDgK5cMqr5DahoKGr9sQD_NIztpmRgpzb0uJMx815X1UICWoEVRR5NcE6jSg5wWLWxw'], 'CloudFront-Forwarded-Proto': ['https'], 'CloudFront-Is-Desktop-Viewer': ['true'], 'CloudFront-Is-Mobile-Viewer': ['false'], 'CloudFront-Is-SmartTV-Viewer': ['false'], 'CloudFront-Is-Tablet-Viewer': ['false'], 'CloudFront-Viewer-ASN': ['18403'], 'CloudFront-Viewer-Country': ['VN'], 'Host': ['jz6rptxoo9.execute-api.ap-southeast-1.amazonaws.com'], 'Postman-Token': ['5455a33c-eb9a-40ca-a00a-f13b39428486'], 'User-Agent': ['PostmanRuntime/7.32.2'], 'Via': ['1.1 e0c6c6bdfde70ef727e4bf48a0402bf6.cloudfront.net (CloudFront)'], 'X-Amz-Cf-Id': ['q9RFTe9PHyDzF5AzOlml91uBLSNiDmJXio3ndagGNIiSXYx1Q4kU9Q=='], 'X-Amzn-Trace-Id': ['Root=1-644e7373-2874307261cd8ec765af4719'], 'X-Forwarded-For': ['*************, ************'], 'X-Forwarded-Port': ['443'], 'X-Forwarded-Proto': ['https']}, 'queryStringParameters': None, 'multiValueQueryStringParameters': None, 'pathParameters': {'connectionId': '42053fff-2db2-44fb-bacb-4a63845ac693'}, 'stageVariables': None, 'requestContext': {'resourceId': 'q0k7nf', 'resourcePath': '/auth/{connectionId}/auth_link', 'httpMethod': 'GET', 'extendedRequestId': 'EMb6IFrWyQ0FQjQ=', 'requestTime': '30/Apr/2023:13:56:03 +0000', 'path': '/dev/auth/42053fff-2db2-44fb-bacb-4a63845ac693/auth_link', 'accountId': '************', 'protocol': 'HTTP/1.1', 'stage': 'dev', 'domainPrefix': 'jz6rptxoo9', 'requestTimeEpoch': *************, 'requestId': '379ce2ee-68c5-4fcd-a49c-053fa6201d55', 'identity': {'cognitoIdentityPoolId': None, 'accountId': None, 'cognitoIdentityId': None, 'caller': None, 'sourceIp': '*************', 'principalOrgId': None, 'accessKey': None, 'cognitoAuthenticationType': None, 'cognitoAuthenticationProvider': None, 'userArn': None, 'userAgent': 'PostmanRuntime/7.32.2', 'user': None}, 'domainName': 'jz6rptxoo9.execute-api.ap-southeast-1.amazonaws.com', 'apiId': 'jz6rptxoo9'}, 'body': None, 'isBase64Encoded': False}

callback_event = {'version': '2.0', 'routeKey': 'GET /auth/callback', 'rawPath': '/auth/callback', 'rawQueryString': 'code=0_116032_Dq8uigmRd0NBGRPslGC3CbcZ157010&state=42053fff-2db2-44fb-bacb-4a63845ac693', 'headers': {'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'accept-encoding': 'gzip, deflate, br', 'accept-language': 'vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5', 'content-length': '0', 'host': 'xyynnhrfy2.execute-api.ap-southeast-1.amazonaws.com', 'referer': 'https://auth.lazada.com/', 'sec-ch-ua': '"Chromium";v="112", "Google Chrome";v="112", "Not:A-Brand";v="99"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'sec-fetch-dest': 'document', 'sec-fetch-mode': 'navigate', 'sec-fetch-site': 'cross-site', 'sec-fetch-user': '?1', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'x-amzn-trace-id': 'Root=1-6450886d-79b287fd57c51bc62a731e2a', 'x-forwarded-for': '*************', 'x-forwarded-port': '443', 'x-forwarded-proto': 'https'}, 'queryStringParameters': {'code': '0_116032_Dq8uigmRd0NBGRPslGC3CbcZ157010', 'state': '42053fff-2db2-44fb-bacb-4a63845ac693'}, 'requestContext': {'accountId': '************', 'apiId': 'xyynnhrfy2', 'domainName': 'xyynnhrfy2.execute-api.ap-southeast-1.amazonaws.com', 'domainPrefix': 'xyynnhrfy2', 'http': {'method': 'GET', 'path': '/auth/callback', 'protocol': 'HTTP/1.1', 'sourceIp': '*************', 'userAgent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'}, 'requestId': 'ERpBLhceSQ0EMmQ=', 'routeKey': 'GET /auth/callback', 'stage': '$default', 'time': '02/May/2023:03:50:05 +0000', 'timeEpoch': *************}, 'isBase64Encoded': False}

connection_event = {'version': '2.0', 'routeKey': 'GET /auth/{connectionId}/test', 'rawPath': '/auth/2188e85e-ac1b-47f0-891c-82efcf9200ff/test', 'rawQueryString': 'baseURL=https://bigquery-staging.onexapis.com&accessToken=*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************', 'headers': {'accept': 'application/json, text/plain, */*', 'accept-encoding': 'gzip, deflate, br', 'accept-language': 'en-US,en;q=0.9', 'authorization': 'Bearer ******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************', 'content-length': '0', 'host': 'bigquery-staging.onexapis.com', 'origin': 'https://app.onexapis.com', 'referer': 'https://app.onexapis.com/', 'sec-ch-ua': '"Not.A/Brand";v="8", "Chromium";v="114", "Google Chrome";v="114"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'sec-fetch-dest': 'empty', 'sec-fetch-mode': 'cors', 'sec-fetch-site': 'same-site', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'x-amzn-trace-id': 'Root=1-648e772d-7c0ddc117f5357c904af7b4b', 'x-forwarded-for': '************', 'x-forwarded-port': '443', 'x-forwarded-proto': 'https'}, 'queryStringParameters': {'accessToken': '*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************', 'baseURL': 'https://bigquery-staging.onexapis.com'}, 'requestContext': {'accountId': '************', 'apiId': '68ijoky2q1', 'authorizer': {'jwt': {'claims': {'aud': '4vj4o1fa56pm85k6rrgto6g8s1', 'auth_time': '**********', 'cognito:username': '48c0bf4d-b606-446a-8925-9bb3bad06d61', 'email': '<EMAIL>', 'event_id': '1aabc5e1-917b-4cf6-9f19-cefa4527a034', 'exp': '**********', 'iat': '**********', 'iss': 'https://cognito-idp.ap-southeast-1.amazonaws.com/ap-southeast-1_9qwDAsl5U', 'jti': 'd7c3b6e5-4604-4859-9cf0-4e1cae5f8705', 'origin_jti': 'a505f61b-9249-4d70-accb-c92feb3a9cd5', 'sub': '48c0bf4d-b606-446a-8925-9bb3bad06d61', 'token_use': 'id'}, 'scopes': None}}, 'domainName': 'bigquery-staging.onexapis.com', 'domainPrefix': 'bigquery-staging', 'http': {'method': 'GET', 'path': '/auth/2188e85e-ac1b-47f0-891c-82efcf9200ff/test', 'protocol': 'HTTP/1.1', 'sourceIp': '************', 'userAgent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'}, 'requestId': 'GsePNiLMSQ0EMkw=', 'routeKey': 'GET /auth/{connectionId}/test', 'stage': '$default', 'time': '18/Jun/2023:03:17:01 +0000', 'timeEpoch': 1687058221857}, 'pathParameters': {'connectionId': '2188e85e-ac1b-47f0-891c-82efcf9200ff'}, 'isBase64Encoded': False}


