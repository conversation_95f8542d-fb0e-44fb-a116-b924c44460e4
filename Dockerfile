# Use AWS Lambda Python base image
FROM public.ecr.aws/lambda/python:3.9

# Set working directory to Lambda task root
WORKDIR ${LAMBDA_TASK_ROOT}

# Install dependencies using yum (Amazon Linux)
RUN yum update -y && yum install -y \
    git \
    openssh-clients \
    wget \
    unzip \
    liberation-fonts \
    alsa-lib \
    at-spi2-atk \
    atk \
    cups-libs \
    dbus-libs \
    gdk-pixbuf2 \
    nspr \
    nss \
    libX11-xcb \
    libXcomposite \
    libXdamage \
    libXrandr \
    xdg-utils \
    && yum clean all

# Install Chrome and ChromeDriver with compatible versions
RUN mkdir -p /opt/chrome \
    && yum install -y https://dl.google.com/linux/direct/google-chrome-stable-current_x86_64.rpm \
    && CHROME_VERSION=$(google-chrome --version | cut -d " " -f3 | cut -d "." -f1) \
    && CHROMEDRIVER_VERSION=$(curl -s "https://chromedriver.storage.googleapis.com/LATEST_RELEASE_${CHROME_VERSION}") \
    && wget -O /tmp/chromedriver.zip "https://chromedriver.storage.googleapis.com/${CHROMEDRIVER_VERSION}/chromedriver_linux64.zip" \
    && unzip /tmp/chromedriver.zip -d /opt/ \
    && rm /tmp/chromedriver.zip \
    && chmod +x /opt/chromedriver \
    && cp /usr/bin/google-chrome /opt/chrome/chrome \
    && chmod +x /opt/chrome/chrome

# Copy private key and known_hosts
COPY .ssh/id_ed25519 /root/.ssh/id_ed25519
RUN chmod 600 /root/.ssh/id_ed25519
COPY .ssh/known_hosts /root/.ssh/known_hosts

# Copy requirements first to cache layer
COPY requirements.txt ./
RUN pip install --no-cache-dir --upgrade pip \
    && pip install --no-cache-dir -r requirements.txt

# Copy source code
COPY src ./src

# Chrome and ChromeDriver already installed above

ENV FUNCTION_HANDLER=BookingsApi
ENV PYTHONPATH=${LAMBDA_TASK_ROOT}

# Set the CMD to your handler (could also be done as a parameter override via environment variable)
CMD ["src.main.handler"]