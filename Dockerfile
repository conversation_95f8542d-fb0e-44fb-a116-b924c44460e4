# Use AWS Lambda Python base image
FROM public.ecr.aws/lambda/python:3.9

# Set working directory to Lambda task root
WORKDIR ${LAMBDA_TASK_ROOT}

# Install dependencies using yum (Amazon Linux)
RUN yum update -y && yum install -y \
    git \
    openssh-clients \
    wget \
    unzip \
    liberation-fonts \
    alsa-lib \
    at-spi2-atk \
    atk \
    cups-libs \
    dbus-libs \
    gdk-pixbuf2 \
    nspr \
    nss \
    libX11-xcb \
    libXcomposite \
    libXdamage \
    libXrandr \
    libXext \
    libXi \
    libXtst \
    libXScrnSaver \
    libXcursor \
    libXfixes \
    libXrender \
    pango \
    cairo \
    gtk3 \
    xdg-utils \
    && yum clean all

# Copy private key and known_hosts
COPY .ssh/id_ed25519 /root/.ssh/id_ed25519
RUN chmod 600 /root/.ssh/id_ed25519
COPY .ssh/known_hosts /root/.ssh/known_hosts

# Copy requirements first to cache layer
COPY requirements.txt ./
RUN pip install --no-cache-dir --upgrade pip \
    && pip install --no-cache-dir -r requirements.txt

# Copy source code
COPY src ./src

# Install Chrome and ChromeDriver (latest compatible versions)
RUN mkdir -p /opt/chrome && \
    # Install Google Chrome
    curl -sSL https://dl.google.com/linux/direct/google-chrome-stable-current_x86_64.rpm -o /tmp/chrome.rpm && \
    yum localinstall -y /tmp/chrome.rpm && \
    # Get Chrome version and download matching ChromeDriver
    CHROME_VERSION=$(google-chrome --version | grep -oP '\d+\.\d+\.\d+' | head -1) && \
    CHROME_MAJOR_VERSION=$(echo $CHROME_VERSION | cut -d. -f1) && \
    echo "Chrome version: $CHROME_VERSION, Major: $CHROME_MAJOR_VERSION" && \
    # Download ChromeDriver for Chrome major version
    if [ "$CHROME_MAJOR_VERSION" -ge "115" ]; then \
        # For Chrome 115+, use Chrome for Testing API
        CHROMEDRIVER_VERSION=$(curl -s "https://googlechromelabs.github.io/chrome-for-testing/LATEST_RELEASE_$CHROME_MAJOR_VERSION" || echo "119.0.6045.105") && \
        CHROMEDRIVER_URL="https://storage.googleapis.com/chrome-for-testing-public/$CHROMEDRIVER_VERSION/linux64/chromedriver-linux64.zip"; \
    else \
        # For Chrome < 115, use legacy ChromeDriver API
        CHROMEDRIVER_VERSION=$(curl -s "https://chromedriver.storage.googleapis.com/LATEST_RELEASE_$CHROME_MAJOR_VERSION" || echo "114.0.5735.90") && \
        CHROMEDRIVER_URL="https://chromedriver.storage.googleapis.com/$CHROMEDRIVER_VERSION/chromedriver_linux64.zip"; \
    fi && \
    echo "ChromeDriver version: $CHROMEDRIVER_VERSION" && \
    echo "ChromeDriver URL: $CHROMEDRIVER_URL" && \
    # Download and install ChromeDriver
    curl -sSL "$CHROMEDRIVER_URL" -o /tmp/chromedriver.zip && \
    unzip /tmp/chromedriver.zip -d /tmp && \
    # Handle different directory structures
    if [ -f "/tmp/chromedriver-linux64/chromedriver" ]; then \
        cp /tmp/chromedriver-linux64/chromedriver /opt/chromedriver; \
    elif [ -f "/tmp/chromedriver" ]; then \
        cp /tmp/chromedriver /opt/chromedriver; \
    else \
        echo "ChromeDriver binary not found!" && exit 1; \
    fi && \
    # Setup Chrome binary in /opt
    cp /usr/bin/google-chrome /opt/chrome/chrome && \
    chmod +x /opt/chrome/chrome /opt/chromedriver && \
    # Verify installation
    echo "Chrome version: $(/opt/chrome/chrome --version)" && \
    echo "ChromeDriver version: $(/opt/chromedriver --version)" && \
    # Cleanup
    rm -f /tmp/chrome.rpm /tmp/chromedriver.zip && \
    rm -rf /tmp/chromedriver-linux64 /tmp/chromedriver

ENV FUNCTION_HANDLER=BookingsApi
ENV PYTHONPATH=${LAMBDA_TASK_ROOT}

# Set the CMD to your handler (could also be done as a parameter override via environment variable)
CMD ["src.main.handler"]