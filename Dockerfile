# Use AWS Lambda Python base image
FROM public.ecr.aws/lambda/python:3.9

# Set working directory to Lambda task root
WORKDIR ${LAMBDA_TASK_ROOT}

# Install dependencies using yum (Amazon Linux)
RUN yum update -y && yum install -y \
    git \
    openssh-clients \
    wget \
    unzip \
    liberation-fonts \
    alsa-lib \
    at-spi2-atk \
    atk \
    cups-libs \
    dbus-libs \
    gdk-pixbuf2 \
    nspr \
    nss \
    libX11-xcb \
    libXcomposite \
    libXdamage \
    libXrandr \
    xdg-utils \
    && yum clean all

# Copy private key and known_hosts
COPY .ssh/id_ed25519 /root/.ssh/id_ed25519
RUN chmod 600 /root/.ssh/id_ed25519
COPY .ssh/known_hosts /root/.ssh/known_hosts

# Copy requirements first to cache layer
COPY requirements.txt ./
RUN pip install --no-cache-dir --upgrade pip \
    && pip install --no-cache-dir -r requirements.txt

# Copy source code
COPY src ./src

# Use existing Chrome files from otp folder (as requested)
RUN mkdir -p /opt/chrome
COPY src/libs/otp/headless-chromium /opt/chrome/chrome
COPY src/libs/otp/chromedriver /opt/chromedriver
RUN chmod +x /opt/chrome/chrome /opt/chromedriver

ENV FUNCTION_HANDLER=BookingsApi
ENV PYTHONPATH=${LAMBDA_TASK_ROOT}

# Set the CMD to your handler (could also be done as a parameter override via environment variable)
CMD ["src.main.handler"]