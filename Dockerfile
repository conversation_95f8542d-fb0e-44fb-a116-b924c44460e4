FROM python:3.9-slim

WORKDIR /app

# Install dependencies
RUN apt-get update && apt-get install -y \
    git \
    openssh-client \
    wget \
    unzip \
    fonts-liberation \
    libasound2 \
    libatk-bridge2.0-0 \
    libatk1.0-0 \
    libcups2 \
    libdbus-1-3 \
    libgdk-pixbuf2.0-0 \
    libnspr4 \
    libnss3 \
    libx11-xcb1 \
    libxcomposite1 \
    libxdamage1 \
    libxrandr2 \
    xdg-utils \
    libu2f-udev \
    libvulkan1 \
    && rm -rf /var/lib/apt/lists/*

# Copy private key and known_hosts
COPY .ssh/id_ed25519 /root/.ssh/id_ed25519
RUN chmod 600 /root/.ssh/id_ed25519
COPY .ssh/known_hosts /root/.ssh/known_hosts

# Copy requirements first to cache layer
COPY requirements.txt ./
RUN pip install --no-cache-dir --upgrade pip \
    && pip install --no-cache-dir -r requirements.txt

# Copy source code
COPY src ./src

# Copy .env file for environment variables
COPY .env ./

# Copy headless-chromium and chromedriver to /opt
RUN mkdir -p /opt/chrome
COPY src/libs/otp/headless-chromium /opt/chrome/chrome
COPY src/libs/otp/chromedriver /opt/chromedriver
RUN chmod +x /opt/chrome/chrome /opt/chromedriver

# Set environment variables
ENV FUNCTION_HANDLER=BookingsApi
ENV PYTHONPATH=/app
ENV USER_POOL_ID=ap-southeast-1_Tmn6Tbm0H
ENV APP_CLIENT_ID=3fh2s28e3g8l23068d6j573l9a
ENV LOG_LEVEL=40
ENV ENV=dev
ENV PROFILE=onexapis
ENV APP_URL=https://app-staging.onexapis.com
ENV API_URL=https://api-staging.onexapis.com
ENV SERVICE=onexapisBambufit
ENV TIMEOUT=10
ENV BUCKET_NAME=onexapis
ENV ELASTIC_SEARCH_HOST=https://**************:9200
ENV ELASTIC_SEARCH_USERNAME=elastic
ENV ELASTIC_SEARCH_PASSWORD=OneXAPIs_NoLi@30071991!

CMD ["python", "src/main.py"]