sampleWebhook:
  image:
    name: bambufit
  environment:
    FUNCTION_HANDLER: sampleWebhook
  events:
    - httpApi:
        path: /webhook
        method: post

actions:
  image:
    name: bambufit
  environment:
    FUNCTION_HANDLER: actions
  events:
    - httpApi:
        path: /actions
        method: get
        authorizer:
          name: onexapisAuthorizer

initActions:
  image:
    name: bambufit
  environment:
    FUNCTION_HANDLER: initActions
  events:
    - httpApi:
        path: /actions/{action_name}/init
        method: post
        authorizer:
          name: onexapisAuthorizer