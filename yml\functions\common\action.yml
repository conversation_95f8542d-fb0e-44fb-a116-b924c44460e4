sampleWebhook:
  handler: src.apis.actions.webhook
  events:
    - httpApi:
        path: /webhook
        method: post

actions:
  handler: src.apis.actions.list_actions
  events:
    - httpApi:
        path: /actions
        method: get
        authorizer:
          name: onexapisAuthorizer

initActions:
  handler: src.apis.actions.init_actions
  events:
    - httpApi:
        path: /actions/{action_name}/init
        method: post
        authorizer:
          name: onexapisAuthorizer