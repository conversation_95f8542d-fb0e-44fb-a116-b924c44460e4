event = {
  "StateName": "Customer.SyncCustomer",
  "StartTime": "2023-11-08T16:30:10.198Z",
  "Payload": {
    "action_id": "470a9c6d-6b97-4df5-b3c2-bcc6ba9d3a87",
    "flow": {
      "scheduler": "30 16 * * *",
      "company_id": "0e274936-3501-4612-a46a-deece00e5555",
      "app_id": "0a10c412-71b0-4892-97a5-81d1a12f3771",
      "name": "Sync customer from Bambufit",
      "status": "ACTIVE",
      "id": "fea6088d-b79c-4257-8df5-215cde5c399a",
      "arn": "arn:aws:states:ap-southeast-1:536980901052:stateMachine:SyncCustomerFromBambufit",
      "webhook": False,
      "flow_definition": {
        "edges": [
          {
            "type": "edgeNodeAction",
            "label": "edgeNodeAction",
            "targetHandle": None,
            "sourceHandle": None,
            "markerEnd": {
              "type": "arrow"
            },
            "source": "341e51e2-b17f-4cb1-bcf0-7ead421a3675",
            "animated": False,
            "id": "34b0bec1-de53-4a84-aeec-64b71aff615a",
            "data": {},
            "target": "470a9c6d-6b97-4df5-b3c2-bcc6ba9d3a87"
          },
          {
            "type": "buttonedge",
            "label": "buttonedge",
            "targetHandle": None,
            "sourceHandle": "right",
            "markerEnd": {
              "type": "arrow"
            },
            "source": "470a9c6d-6b97-4df5-b3c2-bcc6ba9d3a87",
            "animated": False,
            "id": "a242c396-c040-420d-a105-c1452fd8bfe1",
            "data": {
              "source": "470a9c6d-6b97-4df5-b3c2-bcc6ba9d3a87",
              "target": "ed01b4b3-2100-4278-960d-49625e289bc2"
            },
            "target": "ed01b4b3-2100-4278-960d-49625e289bc2"
          }
        ],
        "nodes": [
          {
            "type": "startNode",
            "position": {
              "x": -125,
              "y": -90
            },
            "extent": None,
            "targetPosition": None,
            "id": "341e51e2-b17f-4cb1-bcf0-7ead421a3675",
            "style": None,
            "data": {
              "label": "startNode"
            },
            "parentNode": None,
            "sourcePosition": None
          },
          {
            "type": "actionNode",
            "position": {
              "x": 175,
              "y": -90
            },
            "extent": None,
            "targetPosition": None,
            "id": "470a9c6d-6b97-4df5-b3c2-bcc6ba9d3a87",
            "style": None,
            "data": {
              "label": {
                "valueAction": "customer.sync_customer",
                "name": "Customer.sync Customer",
                "urlChannel": "https://bambufit.onexapis.com",
                "saveRecord": True,
                "channelName": "Bambufit",
                "imageChannel": "https://onexapisprod.s3.amazonaws.com/bambufit.png",
                "type": "CONNECTION",
                "channelId": "dadbf29d-cb2a-4def-beeb-4a92b757c48a"
              }
            },
            "parentNode": None,
            "sourcePosition": None
          },
          {
            "type": "endNode",
            "position": {
              "x": 475,
              "y": -90
            },
            "extent": None,
            "targetPosition": None,
            "id": "ed01b4b3-2100-4278-960d-49625e289bc2",
            "style": None,
            "data": {
              "label": "endNode"
            },
            "parentNode": None,
            "sourcePosition": None
          }
        ]
      },
      "scheduler_type": "CRON",
      "updated_at": "2023-11-08T04:32:46.408280+00:00",
      "created_at": "2023-10-28T03:44:04.048633+00:00"
    },
    "input": {}
  },
  "ExecutionId": "arn:aws:states:ap-southeast-1:536980901052:execution:SyncCustomerFromBambufit:98aa45af-fa54-1b39-6f22-9b6c19334bb5_362c68a7-762c-5a39-088e-98abe46888cd"
}