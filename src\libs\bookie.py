import pendulum
import requests

API_KEY = 'aa88ec60-b1dc-488a-8e18-a28bc34e2e01'
SYNC_PATIENTS = 'https://apitest.bookie.vn/pkvietanh/BenhNhan/sync'


def bambu_to_bookie(bambu_booking):
    booking_date = pendulum.from_format(bambu_booking['Date'], 'DD/MM/YYYY')
    return {
        "appointmentCode": f"{booking_date.to_date_string()}-{bambu_booking['TT']}",
        "scheduleDate": booking_date.to_iso8601_string(),
        "fromTime": int(bambu_booking["Giờ"]) * 60,
        "patientCode": bambu_booking['Kh<PERSON>ch hàng'].split('ID:')[-1].strip().split(' ')[0].replace('ĐC:', ''),
        "patientName": bambu_booking['Khách hàng'].split('-')[0].strip(),
        "patientMobile": bambu_booking["Điện thoại"],
        "state": 0,
        "patientComeFrom": bambu_booking["Nguồn"] if 'Nguồn' in bambu_booking else bambu_booking["Nguồn/Nhóm"],
        "note": bambu_booking["Nội dung hẹn"],
        "providerCode": bambu_booking["Nhân viên"],
        "clinicCode": bambu_booking["P.K"],
        "responseMessage": ""
    }


def bambu_customer_to_bookie(bambu_customer):
    dob = bambu_customer.get('dob')
    if dob:
        dob = pendulum.from_format(dob, 'DD/MM/YYYY').to_iso8601_string()
    customer = {
        "domain": "pkvietanh",
        "hoTen": bambu_customer['Họ và tên'],
        "dienThoai": bambu_customer['Điện thoại'],
        "ngaySinh": dob,
        "namSinh": 1945,
        "diaChi": bambu_customer['address'],
        "gioiTinh": 0,
        "tienSu": "string",
        'dieuTri': [bambu_treatment_to_bookie(treatment) for treatment in bambu_customer['treatments']],
        'thanhToan': [bambu_payment_to_bookie(payment) for payment in bambu_customer['payments']],
        "lichHen": [
            {
                "ghiChu": bambu_customer['booking']['note'],
                "date": bambu_customer['booking']['scheduleDate']
            }
        ]
    }
    return customer


def get_date(booking):
    return pendulum.from_format(f"{booking['Ngày hẹn']} {booking['Giờ hẹn']}", 'DD-MM-YYYY HH:mm:ss')


def bambu_patient_to_bookie(bambu_customer):
    dob = bambu_customer.get('dob')
    if dob:
        dob = pendulum.from_format(dob, 'DD/MM/YYYY').to_iso8601_string()
    patient_code = bambu_customer['booking']['patientCode'] if 'booking' in bambu_customer else bambu_customer[
        'patientCode']
    patient_id = bambu_customer['booking']['patientId'] if 'booking' in bambu_customer else bambu_customer[
        'patientId']
    processes = []
    for treatment in bambu_customer['treatments']:
        if 'Nội dung / điều trị' in treatment and isinstance(treatment['Nội dung / điều trị'], dict) and isinstance(treatment['Nội dung / điều trị']['processes'], list):
            processes.extend(treatment['Nội dung / điều trị']['processes'])
    patient = {
        # "id": bambu_customer['booking']['patientCode'],
        "active": True,
        "domain": "pkvietanh",
        "ma": patient_id,
        "maHoSo": patient_code,
        "ten": "",
        "hoTen": bambu_customer['name'],
        "dienThoai": bambu_customer['phone'],
        "ngaySinh": dob,
        "namSinh": 0,
        "diaChi": bambu_customer['address'],
        "gioiTinh": 0,
        "tienSu": "string",
        "primeNumberOfTag": 0,
        "email": bambu_customer['email'],
        "nguonBenhNhanId": "",
        "ngayKhamCuoi": "2022-10-31T15:03:08.483Z",
        "tienTrinhDieuTri": [
            {
                "ngayThucHien": pendulum.from_format(process['Ngày'], 'DD-MM-YYYY').to_iso8601_string(),
                "tenTienTrinh": process['Bước tiến trình'],
                "moTaCongViec": process['Nội dung điều trị'],
                "bacSiThucHien": process['Bác sỹ'],
                "ghiChu": process['Ghi chú']
            } for process in processes
        ],
        "dieuTri": [
            {
                "noiDungDieuTri": treatment['Nội dung / điều trị']['NoiDungDieuTri'] if isinstance(treatment[
                                                                                                       'Nội dung / điều trị'],
                                                                                                   dict) else treatment[
                    'Nội dung / điều trị'],
                "ngayDieuTri": f"{treatment['Lần'][-4:]}-{treatment['Lần'][-7:-5]}-{treatment['Lần'][-10:-8]}T{treatment['Giờ']}.000Z",
                "ghiChu": treatment['Nội dung / điều trị']['GhiChu'] if isinstance(treatment['Nội dung / điều trị'],
                                                                                   dict) else '',
                "thanhTien": treatment['Thành tiền'],
                "soTienThu": treatment['Đã thu'],
                "trangThai": treatment.get('Trạng thái', ""),
                "bacSiThucHien": treatment['Nội dung / điều trị']['BacSy']
            } for treatment in bambu_customer['treatments']
        ],
        'thanhToan': [bambu_payment_to_bookie(payment) for payment in bambu_customer['payments']]
    }
    if 'bookings' in bambu_customer:
        patient['lichHen'] = [
            {
                "ghiChu": f"{booking['Tên lịch hẹn']} - {booking['Nội dung hẹn']}",
                "date": get_date(booking).to_iso8601_string()
            } for booking in bambu_customer['bookings'] if get_date(booking) > pendulum.now()
        ]
    else:
        patient["lichHen"] = [
            {
                "ghiChu": bambu_customer['booking']['note'],
                "date": bambu_customer['booking']['scheduleDate']
            }
        ]
    return patient


def bambu_treatment_to_bookie(bambu_treatment):
    return {
        "ghiChu": bambu_treatment['Ghi chú'],
        "ngayDieuTri": bambu_treatment['Lần'][-10:],
        "noiDungDieuTri": bambu_treatment['Thủ thuật điều trị'],
        "thanhTien": bambu_treatment['Thành tiền'],
        "soTienThu": bambu_treatment['Đã thu']
    }


def bambu_payment_to_bookie(bambu_payment):
    payment_date = pendulum.from_format(bambu_payment['Ngày'], 'DD/MM/YYYY')
    return {
        "maPhieu": bambu_payment['Mã phiếu'],
        "ngayThanhToan": payment_date.to_iso8601_string(),
        "phuongThucThanhToan": bambu_payment['Ghi chú'],
        "soTien": int(bambu_payment['Số tiền'].replace(',', '')),
        "bacSi": bambu_payment['Bác sỹ'] if 'Bác sỹ' in bambu_payment else '',
        "noiDung": bambu_payment['Nội dung'],
        "nguoiCapNhat": bambu_payment['Cập nhật']
    }


def bambu_finance_report_to_bookie(bambu_finance_report):
    return {
        "maPhieu": "string",
        "ngayThanhToan": bambu_finance_report['Ngày tháng'],
        "phuongThucThanhToan": bambu_finance_report['Nguồn quỹ'],
        "soTien": int(bambu_finance_report['Thu'].replace(',', '')),
        "bacSi": bambu_finance_report.get('Nhân viên', ''),
        "noiDung": bambu_finance_report['Lý do'],
        "hoTen": bambu_finance_report['Tên đối tượng'],
        "dienThoai": bambu_finance_report['Điện thoại'],
        "nguoiCapNhat": "string"
    }


def sync_patients_api(url, api_key, patients):
    headers = {
        'ApiKey': api_key
    }
    retry = 3
    counter = retry
    responses = {}
    while counter > 0:
        response = requests.post(url, json=patients, headers=headers)
        if response.status_code > 200:
            counter -= 1
            responses[f'retry {retry - counter}'] = response.text
            continue
        responses[f'retry {retry - counter}'] = response.json()
        break
    return responses
