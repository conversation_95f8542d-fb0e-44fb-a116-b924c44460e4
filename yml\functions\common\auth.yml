schema:
  image:
    name: bambufit
  environment:
    FUNCTION_HANDLER: schema
  events:
    - httpApi:
        path: /auth/schema
        method: get
        authorizer:
          name: onexapisAuthorizer

authLink:
  image:
    name: bambufit
  environment:
    FUNCTION_HANDLER: authLink
  events:
    - httpApi:
        path: /auth/{connection_id}/auth_link
        method: get
        authorizer:
          name: onexapisAuthorizer

testConnection:
  image:
    name: bambufit
  environment:
    FUNCTION_HANDLER: testConnection
  events:
    - httpApi:
        path: /auth/{connection_id}/test
        method: get
        authorizer:
          name: onexapisAuthorizer

callback:
  image:
    name: bambufit
  environment:
    FUNCTION_HANDLER: callback
  events:
    - httpApi:
        path: /auth/callback
        method: get