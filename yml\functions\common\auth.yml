schema:
  handler: src.apis.auth.schema
  layers:
    - Ref: PythonRequirementsLambdaLayer
  events:
    - httpApi:
        path: /auth/schema
        method: get
        authorizer:
          name: onexapisAuthorizer

authLink:
  handler: src.apis.auth.auth_link
  layers:
    - Ref: PythonRequirementsLambdaLayer
  events:
    - httpApi:
        path: /auth/{connection_id}/auth_link
        method: get
        authorizer:
          name: onexapisAuthorizer

testConnection:
  handler: src.apis.auth.test_connection
  layers:
    - Ref: PythonRequirementsLambdaLayer
  events:
    - httpApi:
        path: /auth/{connection_id}/test
        method: get
        authorizer:
          name: onexapisAuthorizer

callback:
  handler: src.apis.auth.callback
  layers:
    - Ref: PythonRequirementsLambdaLayer
  events:
    - httpApi:
        path: /auth/callback
        method: get