from nolicore.utils.api import api_message
from nolicore.utils.aws.decorator import as_api
from nolicore.utils.aws.request import ApiGatewayRequest

from src.libs.bambu import Bambufit
from src.models.connection import BambufitConnection, BambufitConnectionSchema


@as_api()
def schema(api_request: ApiGatewayRequest):
    connection_schema = BambufitConnectionSchema(BambufitConnection)
    return {
        'schema': connection_schema.json_schema(),
        'sample': connection_schema.sample(),
    }


@as_api()
def auth_link(api_request: ApiGatewayRequest):
    # generate auth link
    raise NotImplemented


@as_api()
def test_connection(api_request: ApiGatewayRequest):
    connection_id = api_request.path_parameters['connection_id']
    bambufit = Bambufit.from_connection(connection_id, api_request.company_id)
    bambufit.test_connection_success(connection_id, api_request.company_id)
    return api_message('Test connection successfully!')


@as_api()
def callback(api_request: ApiGatewayRequest):
    # store access token
    raise NotImplemented
