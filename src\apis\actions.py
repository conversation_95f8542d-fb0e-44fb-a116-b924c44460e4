import importlib
import os
import pkgutil
import uuid

import pendulum
import requests
from nolicore.utils.api import api_message
from nolicore.utils.aws.decorator import as_api
from nolicore.utils.aws.request import ApiGatewayRequest
from nolicore.utils.utils import SERVICE, logger
from onexapis.models.webhook import WebhookModel
from onexapis.utils.service import API_URL


@as_api()
def list_actions(api_request: ApiGatewayRequest):
    actions = {}
    for loader, action_group, is_pkg in pkgutil.walk_packages(["src/actions"]):
        for _, action_file, _ in pkgutil.walk_packages([f"src/actions/{action_group}"]):
            # import the module dynamically
            module = importlib.import_module(f"src.actions.{action_group}.{action_file}")
            try:
                schema = getattr(module, 'schema')
                sample = getattr(module, 'sample')
                actions[f'{action_group}.{action_file}'] = {
                    'schema': schema(),
                    'sample': sample()
                }
            except AttributeError:
                pass
    return actions


@as_api()
def init_actions(api_request: ApiGatewayRequest):
    action_name = api_request.path_parameters['action_name']
    module = importlib.import_module(f"src.actions.{action_name}")
    try:
        init_action = getattr(module, 'init')
        action = {
            **api_request.body,
            'company_id': api_request.company_id,
            'id': str(uuid.uuid4()),
            'updated_at': pendulum.now().to_iso8601_string(),
            'created_at': pendulum.now().to_iso8601_string()
        }
        init_action(action)
    except AttributeError:
        pass
    return api_message('Action initiated')


@as_api()
def webhook(api_request: ApiGatewayRequest):
    event = api_request.body
    logger.info(event)
    seller_id = event['seller_id']
    message_type = event['message_type']
    topic_id = f'{SERVICE}-{seller_id}-{message_type}'
    webhook_subscription = WebhookModel.by_key({
        'id': topic_id
    })
    if webhook_subscription:
        requests.post(f'{API_URL}/flows/{str(webhook_subscription.attributes.flow_id)}/webhook', json=event)
    return api_message('Success!')
