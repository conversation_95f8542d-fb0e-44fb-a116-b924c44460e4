import os
from dataclasses import dataclass

from marshmallow import fields
from nolicore.adapters.db.model import AttributesSchema
from onexapis.models.action import ActionAttributes, ConnectionActionAttributes, ConnectionActionSchema, \
    ActionRecordModel
from onexapis.utils.decorator import action
from onexapis.utils.service import get_action_alias, get_lambda_function_arn

from src.libs.bambu import Bambufit
from src.libs.bookie import sync_patients_api

action_name = os.path.splitext(os.path.basename(__file__))[0]


@dataclass
class Settings:
    bookie_api: str
    bookie_url: str


class SettingsSchema(AttributesSchema):
    bookie_api = fields.Str(required=True)
    bookie_url = fields.Url(required=True)

    def sample(self):
        return {
            'bookie_api': 'aa88ec60-b1dc-488a-8e18-a28bc34e2e01',
            'bookie_url': 'https://apitest.bookie.vn/pkvietanh/BenhNhan/sync'
        }


class SyncCustomerSchema(ConnectionActionSchema):
    settings = fields.Nested(SettingsSchema(Settings), required=True)

    def sample(self):
        return {
            **super(SyncCustomerSchema, self).sample(),
            'input': {},
            'output': {},
            'alias': get_action_alias(action_name),
            'arn': get_lambda_function_arn(action_name),
        }


@dataclass
class SyncCustomerAction(ConnectionActionAttributes):
    settings: Settings


# Every action need to implement 3 key functions
# - sample: to return sample of the action
# - schema: to return schema of the action
# - run: implement logic of execution
def sample():
    return SyncCustomerSchema(ActionAttributes).sample()


def schema():
    _schema = SyncCustomerSchema(SyncCustomerAction).json_schema()
    return _schema


@action(action_schema=SyncCustomerSchema)
def run(action_obj: SyncCustomerAction) -> SyncCustomerAction:
    # prepare records in case we want to save action records
    bambufit = Bambufit.from_connection(action_obj.connection_id, action_obj.company_id)
    # date format 'DD/MM/YYYY'
    from_date = action_obj.input.get('from_date')
    to_date = action_obj.input.get('to_date')
    customers = bambufit.sync_patients(from_date=from_date, to_date=to_date)
    action_obj.records = [
        ActionRecordModel.create_record_from_action(
            action_obj,
            customer['maHoSo'],
            data=customer
        ).attributes_dict for customer in customers
    ]
    responses = []
    for index in range(0, len(customers), 5):
        responses.append(
            sync_patients_api(
                action_obj.settings.bookie_url,
                action_obj.settings.bookie_api,
                customers[index:index + 5]
            )
        )

    action_obj.output = {
        'responses': responses
    }
    return action_obj
