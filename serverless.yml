org: lebinhnguyen1991
app: ${env:SERVICE}
useDotenv: true

service: ${env:SERVICE}

params:
  default:
    aws_profile: onexapis
    env: dev
    user_pool_id: ap-southeast-1_Tmn6Tbm0H
    app_client_id: 3fh2s28e3g8l23068d6j573l9a
    bucket_name: onexapis
    app_url: https://app-staging.onexapis.com
    api_url: https://api-staging.onexapis.com
  prod:
    aws_profile: onexapis_prod
    env: prod
    user_pool_id: ap-southeast-1_Tmn6Tbm0H
    app_client_id: 3fh2s28e3g8l23068d6j573l9a
    bucket_name: onexapis-prod
    app_url: https://app.onexapis.com
    api_url: https://api.onexapis.com

provider:
  name: aws
  region: ap-southeast-1
  profile: ${param:aws_profile, 'onexapis'}
  stage: ${opt:stage, 'dev'}
  memorySize: 128
  timeout: 25
  environment:
    LOG_LEVEL: 40
    ENV: ${param:env}
    USER_POOL_ID: ${param:user_pool_id}
    APP_CLIENT_ID: ${param:app_client_id}
    BUCKET_NAME: ${param:bucket_name}
    FUNCTION_HANDLER: ${env:FUNCTION_HANDLER}
    PROFILE: ${param:aws_profile}
    SERVICE: ${env:SERVICE}
    TIMEOUT: ${env:TIMEOUT}
    OPEN_SEARCH_HOST: ${env:ELASTIC_SEARCH_HOST}
    ELASTIC_SEARCH_USERNAME: ${env:ELASTIC_SEARCH_USERNAME}
    ELASTIC_SEARCH_PASSWORD: ${env:ELASTIC_SEARCH_PASSWORD}
    APP_URL: ${param:app_url}
    API_URL: ${param:api_url}

  ecr:
    images:
      bambufit:
        path: .

  iam: ${file(yml/iam.yml)}

  httpApi:
    cors: true
    authorizers:
      onexapisAuthorizer:
        name: onexapisAuthorizer
        type: jwt
        identitySource: "$request.header.Authorization"
        issuerUrl: "https://cognito-idp.${opt:region, self:provider.region}.amazonaws.com/${opt:aws.cognito.userPoolId, self:provider.environment.USER_POOL_ID}"
        audience:
          - "${opt:aws.cognito.userPoolClientId, self:provider.environment.APP_CLIENT_ID}"

functions:
  - ${file(yml/functions/common/auth.yml)}
  - ${file(yml/functions/common/action.yml)}
  - ${file(yml/functions/booking.yml)}
  - ${file(yml/functions/customer.yml)}

plugins:
  - serverless-dotenv-plugin
  - serverless-prune-plugin

package:
    # Directories and files to include in the deployed package
    patterns:
        - src/**
        - "!src/libs/otp"
        - "!.git/**"
        - "!node_modules/**"

custom: ${file(yml/custom.yml)}