import os

os.environ['USER_POOL_ID'] = 'ap-southeast-1_BCrxqt1qo'
os.environ['APP_CLIENT_ID'] = '3p9avbd007gpqt6dlr8js28pup'
os.environ['AWS_PROFILE'] = 'onexapis'
os.environ['ENV'] = 'dev'
os.environ['LOG_LEVEL'] = '20'

customer_event = {'StateName': 'Customer.SyncCustomer', 'StartTime': '2023-10-26T06:32:12.765Z', 'Payload': {'action_id': 'bd64d7e6-b1c7-486d-98b5-3fc55e3ba73a', 'flow': {'scheduler': '56 5 * * *', 'arn': 'arn:aws:states:ap-southeast-1:220203395173:stateMachine:SyncCustomerFromBambufit', 'flow_definition': {'edges': [{'animated': False, 'data': {}, 'targetHandle': None, 'target': 'bd64d7e6-b1c7-486d-98b5-3fc55e3ba73a', 'type': 'edgeNodeAction', 'markerEnd': {'type': 'arrow'}, 'sourceHandle': None, 'id': 'a77ca499-c28a-4938-8741-d3b0adc06d28', 'label': 'edgeNodeAction', 'source': '1d829faa-80a0-4013-b13f-d4e954061c35'}, {'animated': False, 'data': {'source': 'bd64d7e6-b1c7-486d-98b5-3fc55e3ba73a', 'target': '8aa444e0-cf37-4c17-a9cc-25b47a7154ed'}, 'targetHandle': None, 'target': '8aa444e0-cf37-4c17-a9cc-25b47a7154ed', 'type': 'buttonedge', 'markerEnd': {'type': 'arrow'}, 'sourceHandle': 'right', 'id': '53d898ad-9538-4541-9e4d-c6dd55e4d889', 'label': 'buttonedge', 'source': 'bd64d7e6-b1c7-486d-98b5-3fc55e3ba73a'}], 'nodes': [{'data': {'label': 'startNode'}, 'parentNode': None, 'type': 'startNode', 'targetPosition': None, 'extent': None, 'id': '1d829faa-80a0-4013-b13f-d4e954061c35', 'sourcePosition': None, 'position': {'x': '-125', 'y': '-90'}, 'style': None}, {'data': {'label': {'valueAction': 'customer.sync_customer', 'name': 'Customer.sync Customer', 'urlChannel': 'https://bambufit-staging.onexapis.com', 'saveRecord': False, 'channelName': 'Bambufit', 'imageChannel': 'https://onexapis.s3.amazonaws.com/bambufit.png', 'type': 'CONNECTION', 'channelId': 'f58acbc4-afcf-4078-8511-c22ec08709bb'}}, 'parentNode': None, 'type': 'actionNode', 'targetPosition': None, 'extent': None, 'id': 'bd64d7e6-b1c7-486d-98b5-3fc55e3ba73a', 'sourcePosition': None, 'position': {'x': '175', 'y': '-90'}, 'style': None}, {'data': {'label': 'endNode'}, 'parentNode': None, 'type': 'endNode', 'targetPosition': None, 'extent': None, 'id': '8aa444e0-cf37-4c17-a9cc-25b47a7154ed', 'sourcePosition': None, 'position': {'x': '475', 'y': '-90'}, 'style': None}]}, 'app_id': 'f45b8890-2943-496b-8848-b2c9f9914754', 'webhook': False, 'scheduler_type': 'CRON', 'name': 'Sync customer from Bambufit', 'status': 'ACTIVE', 'id': '055a6b7b-975e-40a8-9b5e-495c6935b6a6', 'company_id': '9e61d187-426a-45ec-914d-7aea8ca7d42d', 'updated_at': '2023-10-26T05:54:25.934121+00:00', 'created_at': '2023-10-26T04:46:25.031630+00:00'}, 'input': {}}, 'ExecutionId': 'arn:aws:states:ap-southeast-1:220203395173:execution:SyncCustomerFromBambufit:7a014a3c-333f-4747-a6d7-94cfc3e0ab29'}

