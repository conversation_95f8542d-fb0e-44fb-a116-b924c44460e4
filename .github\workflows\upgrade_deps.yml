name: Upgrade dependencies

on:
  workflow_dispatch:
    inputs:
      dependency:
        required: true
      tag:
        required: true

jobs:
  upgrade:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout sources
      uses: actions/checkout@v3
    - name: Update dependency
      uses: onexapis/onexapis-lib-maps@main
      with:
        action: upgrade_deps
        dependencies_listing_type: python
        dependency: ${{ github.event.inputs.dependency }}
        tag: ${{ github.event.inputs.tag }}
    - name: Install dependencies
      run: |
        cp requirements.txt temp_requirements.txt
        python -m pip install --upgrade pip
        sed -i "s/ssh:\/\/**************\/\(.*\)\//https:\/\/\1:${{ secrets.ONEXAPIS_LIBS }}@github\.com\/\1\//g" requirements.txt
        pip install -r requirements.txt
        mv temp_requirements.txt requirements.txt
    - name: Run tests
      run: python test.py
    - name: Create Pull Request
      uses: peter-evans/create-pull-request@v5
      with:
        branch: deps-${{ github.event.inputs.dependency }}-${{ github.event.inputs.tag }}
        commit-message: upgrade ${{ github.event.inputs.dependency }} to ${{ github.event.inputs.tag }}
        title: Upgrade ${{ github.event.inputs.dependency }} to ${{ github.event.inputs.tag }}
        token: ${{ secrets.ONEXAPIS_LIBS }}
