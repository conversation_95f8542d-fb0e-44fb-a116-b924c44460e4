from nolicore.utils.aws.decorator import as_api
from nolicore.utils.aws.request import ApiGatewayRequest

from ..libs.bambu import Bambufit
from ..libs.bookie import bambu_finance_report_to_bookie, bambu_patient_to_bookie


@as_api()
def get_customer_by_code(api_request: ApiGatewayRequest):
    connection_id = api_request.path_parameters['connection_id']
    bambufit = Bambufit.from_connection(connection_id, api_request.company_id)
    customer_code = api_request.path_parameters['customer_id']
    customer_id = bambufit.get_customer_id_by_code(customer_code)
    customer = bambufit.get_customer_treatments(customer_id)
    customer_payments = bambufit.get_customer_payments(customer_id)
    customer_bookings = bambufit.get_customer_bookings(customer_id)
    return bambu_patient_to_bookie( {**customer, 'patientId': customer_id, 'bookings': customer_bookings,'payments': customer_payments})
    
@as_api()
def get_customer_by_id(api_request: ApiGatewayRequest):
    connection_id = api_request.path_parameters['connection_id']
    bambufit = Bambufit.from_connection(connection_id, api_request.company_id)
    customer_id = api_request.path_parameters['customer_id']
    customer = bambufit.get_customer_treatments(customer_id)
    customer_payments = bambufit.get_customer_payments(customer_id)
    customer_bookings = bambufit.get_customer_bookings(customer_id)
    return bambu_patient_to_bookie({**customer, 'patientId': customer_id, 'bookings': customer_bookings, 'payments': customer_payments})


@as_api()
def payments(api_request: ApiGatewayRequest):
    connection_id = api_request.path_parameters['connection_id']
    bambufit = Bambufit.from_connection(connection_id, api_request.company_id)
    from_date = api_request.query_string_parameters.get('from_date')
    to_date = api_request.query_string_parameters.get('to_date')
    return [bambu_finance_report_to_bookie(p) for p in bambufit.get_report(from_date, to_date)]
