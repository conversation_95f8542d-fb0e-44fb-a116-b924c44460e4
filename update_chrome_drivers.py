#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to download and update Chrome and ChromeDriver to compatible versions
This will replace the files in src/libs/otp/ folder
"""
import os
import sys
import requests
import zipfile
import json
import shutil
from pathlib import Path

def get_latest_chrome_version():
    """Get the latest stable Chrome version"""
    try:
        # Get Chrome version info from Google's API
        url = "https://versionhistory.googleapis.com/v1/chrome/platforms/linux/channels/stable/versions"
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        
        data = response.json()
        if data.get('versions'):
            latest_version = data['versions'][0]['version']
            print(f"📋 Latest Chrome version: {latest_version}")
            return latest_version
        else:
            # Fallback to a known stable version
            fallback_version = "114.0.5735.90"
            print(f"⚠️  Could not get latest version, using fallback: {fallback_version}")
            return fallback_version
            
    except Exception as e:
        print(f"❌ Error getting Chrome version: {e}")
        # Use a known stable version as fallback
        fallback_version = "114.0.5735.90"
        print(f"🔄 Using fallback version: {fallback_version}")
        return fallback_version

def get_compatible_chromedriver_version(chrome_version):
    """Get compatible ChromeDriver version for given Chrome version"""
    try:
        # Extract major version (e.g., "114" from "114.0.5735.90")
        major_version = chrome_version.split('.')[0]
        
        # Get the latest ChromeDriver for this major version
        url = f"https://chromedriver.storage.googleapis.com/LATEST_RELEASE_{major_version}"
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        
        chromedriver_version = response.text.strip()
        print(f"📋 Compatible ChromeDriver version: {chromedriver_version}")
        return chromedriver_version
        
    except Exception as e:
        print(f"❌ Error getting ChromeDriver version: {e}")
        # Use a known stable version as fallback
        fallback_version = "114.0.5735.90"
        print(f"🔄 Using fallback ChromeDriver version: {fallback_version}")
        return fallback_version

def download_file(url, filename, description):
    """Download a file with progress indication"""
    print(f"📥 Downloading {description}...")
    print(f"   URL: {url}")
    
    try:
        response = requests.get(url, stream=True, timeout=60)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        downloaded = 0
        
        with open(filename, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)
                    if total_size > 0:
                        percent = (downloaded / total_size) * 100
                        print(f"\r   Progress: {percent:.1f}%", end='', flush=True)
        
        print(f"\n✅ Downloaded {description} successfully")
        return True
        
    except Exception as e:
        print(f"\n❌ Error downloading {description}: {e}")
        return False

def download_chrome(version, temp_dir):
    """Download Chrome browser"""
    # Use Chromium build that's compatible with Linux
    build_number = "1108766"  # Known stable build
    url = f"https://storage.googleapis.com/chromium-browser-snapshots/Linux_x64/{build_number}/chrome-linux.zip"
    
    chrome_zip = temp_dir / "chrome-linux.zip"
    
    if download_file(url, chrome_zip, f"Chrome {version}"):
        return chrome_zip
    else:
        return None

def download_chromedriver(version, temp_dir):
    """Download ChromeDriver"""
    url = f"https://chromedriver.storage.googleapis.com/{version}/chromedriver_linux64.zip"
    
    chromedriver_zip = temp_dir / "chromedriver.zip"
    
    if download_file(url, chromedriver_zip, f"ChromeDriver {version}"):
        return chromedriver_zip
    else:
        return None

def extract_and_install(chrome_zip, chromedriver_zip, otp_dir):
    """Extract downloaded files and install them to otp directory"""
    print("📦 Extracting and installing files...")
    
    temp_extract = Path("temp_extract")
    temp_extract.mkdir(exist_ok=True)
    
    try:
        # Extract Chrome
        if chrome_zip and chrome_zip.exists():
            print("   Extracting Chrome...")
            with zipfile.ZipFile(chrome_zip, 'r') as zip_ref:
                zip_ref.extractall(temp_extract)
            
            # Find the chrome binary
            chrome_binary = temp_extract / "chrome-linux" / "chrome"
            if chrome_binary.exists():
                # Backup old file
                old_chrome = otp_dir / "headless-chromium"
                if old_chrome.exists():
                    backup_chrome = otp_dir / "headless-chromium.backup"
                    shutil.copy2(old_chrome, backup_chrome)
                    print(f"   📋 Backed up old Chrome to {backup_chrome}")
                
                # Install new Chrome
                shutil.copy2(chrome_binary, old_chrome)
                os.chmod(old_chrome, 0o755)
                print("   ✅ Chrome installed successfully")
            else:
                print("   ❌ Chrome binary not found in extracted files")
                return False
        
        # Extract ChromeDriver
        if chromedriver_zip and chromedriver_zip.exists():
            print("   Extracting ChromeDriver...")
            with zipfile.ZipFile(chromedriver_zip, 'r') as zip_ref:
                zip_ref.extractall(temp_extract)
            
            # Find the chromedriver binary
            chromedriver_binary = temp_extract / "chromedriver"
            if chromedriver_binary.exists():
                # Backup old file
                old_chromedriver = otp_dir / "chromedriver"
                if old_chromedriver.exists():
                    backup_chromedriver = otp_dir / "chromedriver.backup"
                    shutil.copy2(old_chromedriver, backup_chromedriver)
                    print(f"   📋 Backed up old ChromeDriver to {backup_chromedriver}")
                
                # Install new ChromeDriver
                shutil.copy2(chromedriver_binary, old_chromedriver)
                os.chmod(old_chromedriver, 0o755)
                print("   ✅ ChromeDriver installed successfully")
            else:
                print("   ❌ ChromeDriver binary not found in extracted files")
                return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error during extraction: {e}")
        return False
    
    finally:
        # Cleanup temp directory
        if temp_extract.exists():
            shutil.rmtree(temp_extract)

def verify_installation(otp_dir):
    """Verify that the installed files are working"""
    print("🔍 Verifying installation...")
    
    chrome_path = otp_dir / "headless-chromium"
    chromedriver_path = otp_dir / "chromedriver"
    
    # Check if files exist and are executable
    if not chrome_path.exists():
        print("   ❌ Chrome binary not found")
        return False
    
    if not chromedriver_path.exists():
        print("   ❌ ChromeDriver binary not found")
        return False
    
    # Check permissions
    if not os.access(chrome_path, os.X_OK):
        print("   ❌ Chrome binary is not executable")
        return False
    
    if not os.access(chromedriver_path, os.X_OK):
        print("   ❌ ChromeDriver binary is not executable")
        return False
    
    print("   ✅ All files are present and executable")
    
    # Try to get version info
    try:
        import subprocess
        
        # Get ChromeDriver version
        result = subprocess.run([str(chromedriver_path), "--version"], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            chromedriver_version = result.stdout.strip()
            print(f"   📋 ChromeDriver version: {chromedriver_version}")
        else:
            print("   ⚠️  Could not get ChromeDriver version")
        
        # Get Chrome version (this might not work in headless environment)
        result = subprocess.run([str(chrome_path), "--version"], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            chrome_version = result.stdout.strip()
            print(f"   📋 Chrome version: {chrome_version}")
        else:
            print("   ⚠️  Could not get Chrome version (normal in headless environment)")
        
    except Exception as e:
        print(f"   ⚠️  Could not verify versions: {e}")
    
    return True

def main():
    """Main function to update Chrome and ChromeDriver"""
    print("🚀 Chrome and ChromeDriver Update Tool")
    print("=" * 50)
    
    # Setup directories
    otp_dir = Path("src/libs/otp")
    temp_dir = Path("temp_downloads")
    
    # Check if otp directory exists
    if not otp_dir.exists():
        print(f"❌ OTP directory not found: {otp_dir}")
        print("   Please run this script from the project root directory")
        return False
    
    # Create temp directory
    temp_dir.mkdir(exist_ok=True)
    
    try:
        # Get latest versions
        print("\n🔍 Getting latest version information...")
        chrome_version = get_latest_chrome_version()
        chromedriver_version = get_compatible_chromedriver_version(chrome_version)
        
        print(f"\n📋 Will download:")
        print(f"   Chrome: {chrome_version}")
        print(f"   ChromeDriver: {chromedriver_version}")
        
        # Confirm with user
        response = input("\n❓ Continue with download? (y/N): ").strip().lower()
        if response not in ['y', 'yes']:
            print("❌ Download cancelled by user")
            return False
        
        # Download files
        print("\n📥 Starting downloads...")
        chrome_zip = download_chrome(chrome_version, temp_dir)
        chromedriver_zip = download_chromedriver(chromedriver_version, temp_dir)
        
        if not chrome_zip or not chromedriver_zip:
            print("❌ Download failed")
            return False
        
        # Install files
        print("\n📦 Installing files...")
        if not extract_and_install(chrome_zip, chromedriver_zip, otp_dir):
            print("❌ Installation failed")
            return False
        
        # Verify installation
        if not verify_installation(otp_dir):
            print("❌ Verification failed")
            return False
        
        print("\n🎉 Chrome and ChromeDriver updated successfully!")
        print("\n📋 Next steps:")
        print("1. Test the Docker build: docker build -t bambufit:latest .")
        print("2. Test the application with new Chrome/ChromeDriver")
        print("3. Deploy to AWS Lambda")
        
        return True
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False
    
    finally:
        # Cleanup temp directory
        if temp_dir.exists():
            shutil.rmtree(temp_dir)
            print(f"🧹 Cleaned up temporary files")

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
