import os
import warnings
from urllib.parse import urlparse, parse_qs

import pendulum
from bs4 import BeautifulSoup
from nolicore.utils.exceptions import BadRequest
from nolicore.utils.requests.basic import BasicRequest

from src.libs.bookie import bambu_patient_to_bookie, sync_patients_api, bambu_finance_report_to_bookie, bambu_to_bookie
from src.models.connection import BambufitConnection
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import WebDriverWait

warnings.filterwarnings("ignore", category=UserWarning, module='bs4')

SESSION = {}


class Bambufit(BasicRequest):
    LOGIN_URL = 'https://{domain}.bambufit.vn/login.aspx'
    CUSTOMER_URL = 'https://{domain}.bambufit.vn/default.aspx?dl=31'
    TREATMENT_URL = 'https://{domain}.bambufit.vn/default.aspx?dl=31&cusid={customer_id}'
    PAYMENT_URL = 'https://{domain}.bambufit.vn/default.aspx?dl=318&cusid={customer_id}'
    CUSTOMER_BOOKING_URL = 'https://{domain}.bambufit.vn/default.aspx?dl=315&cusid={customer_id}'
    BOOKING_URL = 'https://{domain}.bambufit.vn/default.aspx?dl=32'
    PAYMENT_REPORT_URL = 'https://{domain}.bambufit.vn/report/rpt_payment.aspx'
    STAGE_DETAIL_URL = 'https://{domain}.bambufit.vn/report/rpt_stagedetail.aspx'

    def __init__(self, domain, username, password):
        super().__init__(f'https://{domain}.bambufit.vn')
        self.domain = domain
        self.username = username
        self.password = password

    @classmethod
    def from_connection(cls, connection_id, company_id):
        bambufit: BambufitConnection = BambufitConnection.get(connection_id, company_id)
        return cls(bambufit.attributes.settings.domain, bambufit.attributes.settings.username,
                   bambufit.attributes.settings.password)

    @classmethod
    def test_connection_success(cls, connection_id, company_id):
        bambufit = BambufitConnection.get(connection_id, company_id)
        bambufit.update({
            'connected': True
        })

    def get_url(self, _url, path_params: dict = None):
        path_params = {'domain': self.domain} if path_params is None else {'domain': self.domain, **path_params}
        _url = _url.format(**path_params)
        return _url

    @property
    def session(self) -> webdriver.Chrome:
        try:
            return SESSION[self.domain]
        except KeyError:
            service = None
            options = Options()
            options.headless = True  # Enable headless mode

            local = os.environ.get('ENV')
            if local is not None:
                options.binary_location = '/opt/chrome/chrome'
                service = webdriver.ChromeService("/opt/chromedriver")

                options.add_argument("--headless")
                options.add_argument("--no-sandbox")
                options.add_argument("--disable-gpu")
                options.add_argument("--disable-dev-shm-usage")
                options.add_argument("--disable-extensions")
                options.add_argument("--disable-infobars")
                options.add_argument("--remote-debugging-port=9222")
                options.add_argument("--window-size=1920x1080")
                options.add_argument("--disable-software-rasterizer")
                options.add_argument("--disable-setuid-sandbox")
                options.add_argument("--no-first-run")
                options.add_argument("--no-zygote")
                options.add_argument("--single-process")

            # Initialize the driver
            driver = webdriver.Chrome(service=service, options=options)
            path_params = {
                'domain': self.domain
            }
            login_url = self.get_url(self.LOGIN_URL, path_params=path_params)
            driver.get(login_url)
            WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "txtuName"))).send_keys(
                self.username)
            driver.find_element(By.ID, "txtPass").send_keys(self.password)
            driver.find_element(By.ID, "cmdLogin").click()
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.ID, "ctl05_lblMember")))
            SESSION[self.domain] = driver
            return driver

    def get_page(self, page_url, data=None, path_params=None, headers=None):
        url = self.get_url(page_url, path_params=path_params)
        self.session.get(url)
        html_content = self.session.page_source
        return BeautifulSoup(html_content, "html.parser")

    def get_customer_page(self, _from=None, _to=None):
        new_customer = self.get_page(self.LOGIN_URL)
        if _from is None:
            return new_customer
        data = {
            '__EVENTTARGET': '',
            '__EVENTARGUMENT': '',
            '__VIEWSTATE': '',
            '__VIEWSTATEGENERATOR': '',
            '__VIEWSTATEENCRYPTED': '',
            '__EVENTVALIDATION': '',
            'ctl04$txtSearch': '',
            'ctl04$txtDDate': '',
            'ctl04$drpEmp:': '',
            'ctl04$cmdSearch': ''
        }
        return self.get_page(self.CUSTOMER_URL, data=data)

    def get_report_page(self, from_date=None, to_date=None):
        payment_page = self.get_page(self.PAYMENT_REPORT_URL)
        if from_date and to_date:
            # Locate the input fields by their IDs
            from_date_input = self.session.find_element(By.ID, 'txtFDate')
            to_date_input = self.session.find_element(By.ID, 'txtTDate')

            # Clear any existing values in the input fields before sending new keys
            from_date_input.click()
            self.session.execute_script("arguments[0].setSelectionRange(0, 0);", from_date_input)
            from_date_input.send_keys(from_date)

            to_date_input.click()
            self.session.execute_script("arguments[0].setSelectionRange(0, 0);", to_date_input)
            to_date_input.send_keys(to_date)

            self.session.find_element(By.ID, 'cmdSearch').click()

            WebDriverWait(self.session, 10).until(
                EC.presence_of_element_located((By.ID, 'pnlForm')))
            html_content = self.session.page_source
            return BeautifulSoup(html_content, "html.parser")
        return payment_page

    def get_stage_detail_page(self, from_date=None, to_date=None):
        stage_detail_page = self.get_page(self.STAGE_DETAIL_URL)
        if from_date and to_date:
            # Locate the input fields by their IDs
            from_date_input = self.session.find_element(By.ID, 'txtFDate')
            to_date_input = self.session.find_element(By.ID, 'txtTDate')

            # Clear any existing values in the input fields before sending new keys
            from_date_input.click()
            self.session.execute_script("arguments[0].setSelectionRange(0, 0);", from_date_input)
            from_date_input.send_keys(from_date)

            to_date_input.click()
            self.session.execute_script("arguments[0].setSelectionRange(0, 0);", to_date_input)
            to_date_input.send_keys(to_date)

            self.session.find_element(By.ID, 'cmdSearch').click()

            WebDriverWait(self.session, 10).until(
                EC.presence_of_element_located((By.ID, 'pnlForm')))
            html_content = self.session.page_source
            return BeautifulSoup(html_content, "html.parser")
        return stage_detail_page

    def get_treatment_page(self, customer_id):
        return self.get_page(self.TREATMENT_URL, path_params={"customer_id": customer_id})

    def get_processes_page(self, link):
        url = f"https://{self.domain}.bambufit.vn{link}"
        return self.get_page(url)

    def get_treatment(self, row):
        treatment_data = []
        cols = row.find_all('td')
        for index, col in enumerate(cols):
            if index != len(cols) - 12:
                treatment_data.append(col.text)
            else:
                try:
                    bac_sy = [e for e in col.find_all('div', {"class": "pad_t3"}) if '' in e.text][0].text.replace(
                        ' ', '')
                    bac_sy = bac_sy.split('  ')[0]
                except IndexError:
                    bac_sy = ''
                process_data = {
                    'NoiDungDieuTri': col.find('b').text + col.find('span').text,
                    'GhiChu': col.find_all('i')[-1].text if col.find('i') else '',
                    'BacSy': bac_sy,
                    'processes': []
                }
                if col.text == '0/0':
                    treatment_data.append(process_data)
                else:
                    if col.find('a') is None:
                        treatment_data.append(process_data)
                        continue
                    links = [link.get('href') for link in col.find_all('a') if 'dl=310' in link.get('href')]
                    if not links:
                        continue
                    link = links[0]
                    processes_page = self.get_processes_page(link)
                    count = 0
                    headers = []
                    previous = None
                    for _row in processes_page.find("table", {"id": "ctl04_dgrd_Data"}).find_all('tr'):
                        if count == 0:
                            for _col in _row.find_all('td'):
                                headers.append(_col.text)
                            count += 1
                            continue
                        data = [_col.text for _col in _row.find_all('td')]
                        if previous is None:
                            new_data = previous = data
                        else:
                            if len(previous) > len(data):
                                new_data = previous[:1] + data
                            else:
                                new_data = data
                            previous = new_data
                        process_data['processes'].append({k: v for k, v in zip(headers, new_data)})
                    treatment_data.append(process_data)
        return treatment_data

    def get_customer_id_by_code(self, customer_code):
        self.get_page(self.CUSTOMER_URL)
        search_input = self.session.find_element(By.ID, 'ctl04_txtSearch')
        search_input.send_keys(customer_code)
        submit_button = self.session.find_element(By.ID, 'ctl04_cmdSearch')
        submit_button.click()
        WebDriverWait(self.session, 10).until(
            EC.presence_of_element_located((By.ID, 'ctl04_dgrd_Data')))
        html_content = self.session.page_source
        customer_page = BeautifulSoup(html_content, "html.parser")
        cusid = None
        for idx, row in enumerate(customer_page.find("table", {"id": "ctl04_dgrd_Data"}).find_all('tr')):
            if idx == 0:
                continue
            table_data = row.find_all('td')
            if table_data[4].text != str(customer_code):
                continue
            url = table_data[3].find('a').attrs.get('href')
            query = urlparse(url).query
            params = parse_qs(query)
            cusid = params.get('cusid', [None])[0]
            break
        if cusid is None:
            raise BadRequest("Customer not found")
        return cusid

    def get_customer_treatments(self, customer_id):
        treatment_page = self.get_treatment_page(customer_id)
        count = 0
        headers = []
        treatments = []
        code = treatment_page.find(id='ctl04_txtCodeID').get('value', '')
        dob = treatment_page.find(id='ctl04_txtDateOfBirth').get('value', '')
        name = treatment_page.find(id='ctl04_txtCustomerName').get('value', '')
        phone = treatment_page.find(id='ctl04_txtMobile').get('value', '')
        email = treatment_page.find(id='ctl04_txtEmail').get('value', '')
        notes = treatment_page.find(id='ctl04_txtNotes').get('value', '')
        address = treatment_page.find(id='ctl04_txtAddress').get('value', '')
        previous = None
        for row in treatment_page.find("table", {"id": "ctl04_grd_Therapeutic"}).find_all('tr'):
            if count == 0:
                for col in row.find_all('th'):
                    headers.append(col.text)
                count += 1
                continue
            data = self.get_treatment(row)
            if previous is None:
                new_data = previous = data
            else:
                if len(previous) > len(data):
                    new_data = previous[:1] + data
                else:
                    new_data = data
                previous = new_data

            treatments.append({k: v for k, v in zip(headers, new_data)})
        return {
            'name': name,
            'phone': phone,
            'email': email,
            'notes': notes,
            'patientCode': code,
            'dob': dob,
            'address': address,
            'treatments': treatments
        }

    def get_payment_page(self, customer_id):
        return self.get_page(self.PAYMENT_URL, path_params={'customer_id': customer_id})

    def get_customer_payments(self, customer_id):
        payment_page = self.get_payment_page(customer_id)
        count = 0
        headers = []
        payments = []
        for row in payment_page.find("table", {"id": "ctl04_grd_Data"}).find_all('tr'):
            if count == 0:
                for col in row.find_all('th'):
                    headers.append(col.text)
                count += 1
                continue
            data = [col.text for col in row.find_all('td')]
            payments.append({k: v for k, v in zip(headers, data)})
        return payments

    def get_customer_bookings_page(self, customer_id):
        return self.get_page(self.CUSTOMER_BOOKING_URL, path_params={'customer_id': customer_id})

    def get_customer_bookings(self, customer_id):
        payment_page = self.get_customer_bookings_page(customer_id)
        count = 0
        headers = []
        payments = []
        for row in payment_page.find("table", {"id": "ctl04_dgrd_Data"}).find_all('tr'):
            if count == 0:
                for col in row.find_all('td'):
                    headers.append(col.text)
                count += 1
                continue
            data = [col.text for col in row.find_all('td')]
            payments.append({k: v for k, v in zip(headers, data)})
        return payments

    def get_customer(self):
        customer = self.get_customer_page()
        count = 0
        headers = []
        patients = []
        for row in customer.find("table", {"id": "ctl04_dgrd_Data"}).find_all('tr'):
            if count == 0:
                for col in row.find_all('td'):
                    headers.append(col.text)
                count += 1
                continue
            data = [col.text for col in row.find_all('td')]
            patients.append({k: v for k, v in zip(headers, data)})
        return [{
            **p,
            **self.get_customer_treatments(p['Mã số']),
            'payments': self.get_customer_payments(p['Mã số'])
        } for p in patients]

    def get_booking_page(self, _from=None, _to=None, params=None):
        new_booking = self.get_page(self.BOOKING_URL)
        if _from is None:
            return new_booking

        self.session.find_element(By.CLASS_NAME, 'fa-filter').click()
        # Locate the input fields by their IDs
        from_date_input = self.session.find_element(By.ID, 'ctl04_txtFDate')
        to_date_input = self.session.find_element(By.ID, 'ctl04_txtTDate')

        # Clear any existing values in the input fields before sending new keys
        from_date_input.clear()
        from_date_input.click()
        from_date_input.send_keys(_from)
        to_date_input.clear()
        to_date_input.click()
        to_date_input.send_keys(_to)

        self.session.find_element(By.ID, 'cmdFilter').click()

        WebDriverWait(self.session, 10).until(
            EC.presence_of_element_located((By.ID, 'ctl04_pnlData')))
        html_content = self.session.page_source
        return BeautifulSoup(html_content, "html.parser")

    def get_booking(self, day=3, _from=None, _to=None, booking_page=None):
        bookings = {}
        booking_page = self.get_booking_page(_from=_from, _to=_to) if not booking_page else booking_page
        for index in range(day):
            previous = None
            count = 0
            headers = ['Date']
            try:
                booking_date = booking_page.find("span", {"id": f"ctl04_rptDate_ctl0{index}_lblDate"}).text.split('-')[
                    0].strip()
            except Exception:
                continue
            try:
                for row in booking_page.find("table", {"id": f"ctl04_rptDate_ctl0{index}_grd_Data"}).find_all('tr'):
                    if count == 0:
                        for col in row.find_all('th'):
                            headers.append(col.text)
                        count += 1
                        continue
                    data = [booking_date] + [col.text for col in row.find_all('td')]
                    if previous is None:
                        new_data = previous = data
                    else:
                        length = len(previous) - len(data)
                        new_data = previous = data[:2] + previous[2:][:length] + data[2:]
                    booking = {k: v for k, v in zip(headers, new_data)}
                    key = booking['Điện thoại'].strip() or booking['Khách hàng'].split('-')[-1].strip().split(' ')[
                        1].strip()
                    key = key.replace('/', '_').replace(':', '_')

                    booking['Điện thoại'] = key
                    bookings[key + booking_date + booking['TT']] = booking

            except AttributeError:
                pass
        return list(bookings.values())

    def get_report(self, from_date=None, to_date=None):
        report_page = self.get_report_page(from_date, to_date)
        count = 0
        headers = []
        payments = []
        try:
            for row in report_page.find("table", {"id": "rptPayGroup_ctl00_grd_data"}).find_all('tr'):
                if count == 0:
                    for col in row.find_all('th'):
                        headers.append(col.text)
                    count += 1
                    continue
                if count == 1:
                    count += 1
                    continue
                data = [col.text for col in row.find_all('td')]
                payments.append({k: v for k, v in zip(headers, data)})
        except AttributeError:
            print('no payment')
        return payments

    def get_stage_details(self, from_date=None, to_date=None):
        stag_detail_page = self.get_stage_detail_page(from_date, to_date)
        # with open('index.html') as ifile:
        #     stag_detail_page = BeautifulSoup(ifile.read(), "html.parser")
        count = 0
        headers = []
        payments = []
        try:
            for row in stag_detail_page.find("table", {"id": "grd_data"}).find_all('tr'):
                if count == 0:
                    for col in row.find_all('th'):
                        headers.append(col.text)
                    count += 1
                    continue
                if count == 1:
                    count += 1
                    continue
                data = [col.text for col in row.find_all('td')]
                payments.append({k: v for k, v in zip(headers, data)})
        except AttributeError:
            print('no payment')
        return [p for p in payments if len(p.keys()) == 8]

    def sync_patients(self, from_date=None, to_date=None):
        customer_treatments = []
        if from_date is None and to_date is None:
            from_date = to_date = pendulum.now().format('DD/MM/YYYY')
        payments = self.get_stage_details(from_date, to_date)
        print(f'loading {len(payments)} payments')
        loaded_patients = set()
        counter = 0
        number_of_payment = len(payments)
        for payment in payments:
            counter += 1
            print(f'{counter}/{number_of_payment}')
            patient_id = payment['ID']
            if patient_id in loaded_patients:
                continue
            loaded_patients.add(patient_id)
            try:
                print(patient_id)
                customer = self.get_customer_treatments(patient_id)
                customer_payments = self.get_customer_payments(patient_id)
                customer_bookings = self.get_customer_bookings(patient_id)
                customer_treatments.append(
                    {**customer, 'patientId': patient_id, 'bookings': customer_bookings,
                     'payments': customer_payments})
            except AttributeError as ex:
                print(f'Could not find customer {patient_id}')
                raise ex
        print(f'loading {len(customer_treatments)} customers')
        patients = [bambu_patient_to_bookie(customer) for customer in customer_treatments]
        print(f'importing {len(patients)} customers')
        return patients


if __name__ == '__main__':
    credentials = {
        'vietanh': {
            'username': 'appnhakhoavietanh',
            'password': 'Vietanhskylake'
        },
        'gems': {
            'username': 'nguyentoan',
            'password': 'nguyentoan'
        }
    }
    bambu = Bambufit('vietanh', 'appnhakhoavietanh', 'Vietanhskylake')
    # customer = bambu.get_customer_treatments('3279')
    # customer_payments = bambu.get_customer_payments('3279')
    # customer_bookings = bambu.get_customer_bookings('3279')
    # customer_bookings = bambu.get_customer_treatments('3279')
    # bambu.sync_patients(from_date='07/05/2024', to_date='07/05/2024')
    # bambu.get_report('07/05/2024', '07/05/2024')
    # payments = [bambu_finance_report_to_bookie(p) for p in bambu.get_report('07/05/2024', '07/05/2024')]
    # bambu.get_booking(_from='07/05/2024', _to='07/05/2024')
    # bambu.get_report('10/05/2024', '10/05/2024')
    # bambu.get_stage_details()
    # bookings = bambu.get_booking(_from='05/05/2025', _to='05/05/2025')
    # tmp = [bambu_to_bookie(b) for b in bookings]
    code = '8082'
    ma = bambu.get_customer_id_by_code(code)
    # bambu.sync_patients(from_date='11/06/2025', to_date='11/06/2025')
    customer = bambu.get_customer_treatments(ma)
    bambu.get_customer_payments(ma)
    bambu.get_customer_bookings(ma)
    bookie_customer = bambu_patient_to_bookie({**customer, 'patientCode': ma})
    # bambu.get_customer_treatments('3279')
    # bambu.get_customer_payments('3279')
    # bambu.get_customer_bookings('3279')
    # customers = bambu.sync_patients()
    # with open('response.json', 'a') as ofile:
    #     for index in range(0, len(customers), 5):
    #         print(f'{index}/{len(customers)}')
    #         response = sync_patients_api(
    #             'https://apiv3.bookie.vn/pkvietanh/BenhNhan/sync',
    #             'aa88ec60-b1dc-488a-8e18-a28bc34e2e01',
    #             customers[index:index + 5]
    #         )
    #         ofile.write(f'{json.dumps(response)}\n')
